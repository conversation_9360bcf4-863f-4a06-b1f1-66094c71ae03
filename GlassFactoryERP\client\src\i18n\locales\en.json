{"nav": {"main": "Main", "operations": "Operations", "humanResources": "Human Resources", "finance": "Finance", "dashboard": "Dashboard", "companySettings": "Company Settings", "sales": "Sales", "purchases": "Purchases", "inventory": "Inventory", "manufacturing": "Manufacturing", "payroll": "Payroll", "accounting": "Accounting", "reports": "Reports"}, "dashboard": {"title": "Dashboard", "subtitle": "Welcome back to your ERP system", "totalSales": "Total Sales", "activeOrders": "Active Orders", "inventoryValue": "Inventory Value", "employees": "Employees", "recentInvoices": "Recent Sales Invoices", "quickActions": "Quick Actions", "systemAlerts": "System Alerts", "newSalesInvoice": "New Sales Invoice", "stockReceipt": "Stock Receipt", "addEmployee": "Add Employee", "generateReport": "Generate Report", "lowStockAlert": "Low Stock Alert", "pendingApprovals": "Pending Approvals", "backupComplete": "Backup Complete", "viewAll": "View All"}, "company": {"title": "Company Settings", "subtitle": "Manage your company information and logo", "companyInformation": "Company Information", "companyLogo": "Company Logo", "uploadLogo": "Upload Logo", "supportedFormats": "Supported formats: JPG, PNG, SVG. Max size: 2MB", "companyName": "Company Name", "phone": "Phone", "email": "Email", "taxNumber": "Tax Number", "address": "Address", "saveSettings": "Save Settings", "saving": "Saving...", "enterCompanyName": "Enter company name", "enterPhoneNumber": "Enter phone number", "enterEmailAddress": "Enter email address", "enterTaxNumber": "Enter tax number", "enterCompanyAddress": "Enter company address"}, "sales": {"title": "Sales Management", "subtitle": "Manage customers, services, and sales invoices", "thisMonthSales": "This Month Sales", "totalInvoices": "Total Invoices", "activeCustomers": "Active Customers", "salesInvoices": "Sales Invoices", "customers": "Customers", "servicesProducts": "Services & Products", "salesReports": "Sales Reports", "paymentTracking": "Payment Tracking", "salesSettings": "Sales Settings", "newInvoice": "New Invoice", "addCustomer": "Add Customer", "addService": "Add Service", "viewOutstanding": "View Outstanding", "configure": "Configure", "createManageInvoices": "Create, manage, and track sales invoices with automatic calculations and accounting entries.", "manageCustomerDatabase": "Manage customer database with contact details, billing information, and transaction history.", "manageServiceCatalog": "Manage service catalog with pricing, glass types, and product specifications.", "generateDetailedReports": "Generate detailed sales reports, customer statements, and performance analytics.", "trackPayments": "Track payments, manage outstanding balances, and send payment reminders.", "configureAccounting": "Configure accounting entries, tax rates, and invoice templates."}, "invoices": {"title": "Sales Invoices", "subtitle": "Create and manage sales invoices", "newInvoice": "New Invoice", "searchPlaceholder": "Search by invoice number or customer...", "filterByStatus": "Filter by status", "allStatus": "All Status", "draft": "Draft", "final": "Final", "cancelled": "Cancelled", "moreFilters": "More Filters", "allInvoices": "All Invoices", "invoiceNumber": "Invoice #", "customer": "Customer", "date": "Date", "amount": "Amount", "paid": "Paid", "balance": "Balance", "status": "Status", "actions": "Actions", "noInvoicesFound": "No invoices found", "getStartedMessage": "Get started by creating your first invoice", "createInvoice": "Create Invoice"}, "customers": {"title": "Customers", "subtitle": "Manage customer database and information", "addCustomer": "Add Customer", "addNewCustomer": "Add New Customer", "customerName": "Customer Name", "searchPlaceholder": "Search customers by name, code, or phone...", "noCustomersFound": "No customers found", "adjustSearchCriteria": "Try adjusting your search criteria", "getStartedMessage": "Get started by adding your first customer", "enterCustomerName": "Enter customer name", "enterPhoneNumber": "Enter phone number", "enterTaxNumber": "Enter tax number (optional)", "enterCustomerAddress": "Enter customer address", "cancel": "Cancel", "adding": "Adding...", "created": "Created"}, "purchases": {"title": "Purchases Management", "subtitle": "Manage suppliers, purchase orders, and inventory receipts", "thisMonthPurchases": "This Month Purchases", "purchaseOrders": "Purchase Orders", "activeSuppliers": "Active Suppliers", "suppliers": "Suppliers", "stockReceipts": "Stock Receipts", "purchaseInvoices": "Purchase Invoices", "supplierPayments": "Supplier Payments", "purchaseReports": "Purchase Reports", "addSupplier": "Add Supplier", "createPO": "Create PO", "receiveStock": "Receive Stock", "viewInvoices": "View Invoices", "makePayment": "Make Payment"}, "inventory": {"title": "Inventory Management", "subtitle": "Manage warehouses, items, and stock movements with moving average costing", "manageWarehouses": "Manage Warehouses", "manageItems": "Manage Items", "stockMovements": "Stock Movements", "movementReports": "Movement Reports", "availableItems": "Available Items", "searchPlaceholder": "Search by item name or code...", "filterByWarehouse": "Filter by warehouse", "allWarehouses": "All Warehouses", "currentStockOverview": "Current Stock Overview", "addItem": "Add Item", "stockReceipt": "Stock Receipt", "itemCode": "Item Code", "itemName": "Item Name", "warehouse": "Warehouse", "quantity": "Quantity", "unit": "Unit", "avgCost": "Avg. <PERSON><PERSON>", "totalValue": "Total Value", "noInventoryItems": "No inventory items found", "addItemsMessage": "Add items to your warehouse to get started", "outOfStock": "Out of Stock", "lowStock": "Low Stock", "inStock": "In Stock"}, "payroll": {"title": "Payroll Management", "subtitle": "Manage employees, salaries, and payroll operations", "totalEmployees": "Total Employees", "monthlyPayroll": "Monthly Payroll", "avgSalary": "Avg. <PERSON>", "pending": "Pending", "employees": "Employees", "payrollRecords": "Payroll Records", "searchEmployees": "Search employees...", "searchPayrollRecords": "Search payroll records...", "addEmployee": "Add Employee", "processPayroll": "Process Payroll", "settings": "Settings", "code": "Code", "name": "Name", "position": "Position", "branch": "Branch", "basicSalary": "Basic Salary", "workingHours": "Working Hours", "active": "Active", "inactive": "Inactive", "noEmployeesFound": "No employees found", "addEmployeesMessage": "Add employees to get started with payroll", "payDate": "Pay Date", "baseSalary": "Base Salary", "overtime": "Overtime", "bonuses": "Bonuses", "deductions": "Deductions", "netPay": "Net Pay", "noPayrollRecords": "No payroll records found", "processPayrollMessage": "Process payroll for your employees"}, "accounting": {"title": "Accounting Module", "subtitle": "Manage chart of accounts, journal entries, and financial reports", "totalAssets": "Total Assets", "totalRevenue": "Total Revenue", "totalExpenses": "Total Expenses", "netProfit": "Net Profit", "chartOfAccounts": "Chart of Accounts", "journalEntries": "Journal Entries", "financialReports": "Financial Reports", "addAccount": "Add Account", "newEntry": "New Entry", "parentAccount": "Parent Account", "currentPeriod": "Current period", "operatingCosts": "Operating costs", "margin": "margin", "generalLedger": "General <PERSON><PERSON>", "trialBalance": "Trial Balance", "incomeStatement": "Income Statement", "balanceSheet": "Balance Sheet", "cashFlow": "Cash Flow"}, "common": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "view": "View", "add": "Add", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "download": "Download", "upload": "Upload", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "ok": "OK", "total": "Total", "subtotal": "Subtotal", "discount": "Discount", "tax": "Tax", "required": "Required", "optional": "Optional", "all": "All", "none": "None", "select": "Select", "selectAll": "Select All", "clear": "Clear", "reset": "Reset", "refresh": "Refresh", "retry": "Retry"}, "messages": {"success": {"saved": "Successfully saved", "created": "Successfully created", "updated": "Successfully updated", "deleted": "Successfully deleted"}, "error": {"failed": "Operation failed", "notFound": "Not found", "unauthorized": "Unauthorized", "validation": "Validation error"}}}