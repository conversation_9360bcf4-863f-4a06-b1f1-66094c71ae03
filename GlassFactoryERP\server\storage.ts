import { db } from "./db";
import { eq, desc, and, or, sql, like, gte, lte } from "drizzle-orm";
import * as schema from "@shared/schema";

export interface IStorage {
  // Users
  getUser(id: number): Promise<schema.User | undefined>;
  getUserByUsername(username: string): Promise<schema.User | undefined>;
  createUser(user: schema.InsertUser): Promise<schema.User>;

  // Company
  getCompany(): Promise<schema.Company | undefined>;
  createOrUpdateCompany(company: schema.InsertCompany): Promise<schema.Company>;

  // Customers
  getCustomers(): Promise<schema.Customer[]>;
  getCustomer(id: number): Promise<schema.Customer | undefined>;
  createCustomer(customer: schema.InsertCustomer): Promise<schema.Customer>;
  updateCustomer(id: number, customer: Partial<schema.InsertCustomer>): Promise<schema.Customer>;
  deleteCustomer(id: number): Promise<void>;

  // Suppliers
  getSuppliers(): Promise<schema.Supplier[]>;
  getSupplier(id: number): Promise<schema.Supplier | undefined>;
  createSupplier(supplier: schema.InsertSupplier): Promise<schema.Supplier>;
  updateSupplier(id: number, supplier: Partial<schema.InsertSupplier>): Promise<schema.Supplier>;
  deleteSupplier(id: number): Promise<void>;

  // Services
  getServices(): Promise<schema.Service[]>;
  getService(id: number): Promise<schema.Service | undefined>;
  createService(service: schema.InsertService): Promise<schema.Service>;
  updateService(id: number, service: Partial<schema.InsertService>): Promise<schema.Service>;
  deleteService(id: number): Promise<void>;

  // Sales Invoices
  getSalesInvoices(): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]>;
  getSalesInvoice(id: number): Promise<(schema.SalesInvoice & { customer: schema.Customer; items: (schema.SalesInvoiceItem & { service: schema.Service })[] }) | undefined>;
  createSalesInvoice(invoice: schema.InsertSalesInvoice, items: schema.InsertSalesInvoiceItem[]): Promise<schema.SalesInvoice>;
  updateSalesInvoice(id: number, invoice: Partial<schema.InsertSalesInvoice>): Promise<schema.SalesInvoice>;
  deleteSalesInvoice(id: number): Promise<void>;

  // Employees
  getEmployees(): Promise<schema.Employee[]>;
  getEmployee(id: number): Promise<schema.Employee | undefined>;
  createEmployee(employee: schema.InsertEmployee): Promise<schema.Employee>;
  updateEmployee(id: number, employee: Partial<schema.InsertEmployee>): Promise<schema.Employee>;
  deleteEmployee(id: number): Promise<void>;

  // Payroll
  getPayrollRecords(): Promise<(schema.PayrollRecord & { employee: schema.Employee })[]>;
  getPayrollRecord(id: number): Promise<(schema.PayrollRecord & { employee: schema.Employee }) | undefined>;
  createPayrollRecord(record: schema.InsertPayrollRecord): Promise<schema.PayrollRecord>;
  updatePayrollRecord(id: number, record: Partial<schema.InsertPayrollRecord>): Promise<schema.PayrollRecord>;
  deletePayrollRecord(id: number): Promise<void>;

  // Warehouses
  getWarehouses(): Promise<schema.Warehouse[]>;
  getWarehouse(id: number): Promise<schema.Warehouse | undefined>;
  createWarehouse(warehouse: schema.InsertWarehouse): Promise<schema.Warehouse>;
  updateWarehouse(id: number, warehouse: Partial<schema.InsertWarehouse>): Promise<schema.Warehouse>;
  deleteWarehouse(id: number): Promise<void>;

  // Inventory Items
  getInventoryItems(): Promise<schema.InventoryItem[]>;
  getInventoryItem(id: number): Promise<schema.InventoryItem | undefined>;
  createInventoryItem(item: schema.InsertInventoryItem): Promise<schema.InventoryItem>;
  updateInventoryItem(id: number, item: Partial<schema.InsertInventoryItem>): Promise<schema.InventoryItem>;
  deleteInventoryItem(id: number): Promise<void>;

  // Stock Movements
  getStockMovements(): Promise<(schema.StockMovement & { warehouse: schema.Warehouse; item: schema.InventoryItem })[]>;
  createStockMovement(movement: schema.InsertStockMovement): Promise<schema.StockMovement>;

  // Dashboard Stats
  getDashboardStats(): Promise<{
    totalSales: string;
    activeOrders: number;
    inventoryValue: string;
    employees: number;
  }>;

  // Recent invoices for dashboard
  getRecentSalesInvoices(limit?: number): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<schema.User | undefined> {
    const [user] = await db.select().from(schema.users).where(eq(schema.users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<schema.User | undefined> {
    const [user] = await db.select().from(schema.users).where(eq(schema.users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: schema.InsertUser): Promise<schema.User> {
    const [user] = await db
      .insert(schema.users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getCompany(): Promise<schema.Company | undefined> {
    const [company] = await db.select().from(schema.companies).limit(1);
    return company || undefined;
  }

  async createOrUpdateCompany(company: schema.InsertCompany): Promise<schema.Company> {
    const existing = await this.getCompany();
    if (existing) {
      const [updated] = await db
        .update(schema.companies)
        .set(company)
        .where(eq(schema.companies.id, existing.id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(schema.companies)
        .values(company)
        .returning();
      return created;
    }
  }

  async getCustomers(): Promise<schema.Customer[]> {
    return await db.select().from(schema.customers).orderBy(desc(schema.customers.createdAt));
  }

  async getCustomer(id: number): Promise<schema.Customer | undefined> {
    const [customer] = await db.select().from(schema.customers).where(eq(schema.customers.id, id));
    return customer || undefined;
  }

  async createCustomer(customer: schema.InsertCustomer): Promise<schema.Customer> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.customers);
    const customerCount = Number(count[0].count) + 1;
    const code = `CUST-${customerCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.customers)
      .values({ ...customer, code })
      .returning();
    return created;
  }

  async updateCustomer(id: number, customer: Partial<schema.InsertCustomer>): Promise<schema.Customer> {
    const [updated] = await db
      .update(schema.customers)
      .set(customer)
      .where(eq(schema.customers.id, id))
      .returning();
    return updated;
  }

  async deleteCustomer(id: number): Promise<void> {
    await db.delete(schema.customers).where(eq(schema.customers.id, id));
  }

  async getSuppliers(): Promise<schema.Supplier[]> {
    return await db.select().from(schema.suppliers).orderBy(desc(schema.suppliers.createdAt));
  }

  async getSupplier(id: number): Promise<schema.Supplier | undefined> {
    const [supplier] = await db.select().from(schema.suppliers).where(eq(schema.suppliers.id, id));
    return supplier || undefined;
  }

  async createSupplier(supplier: schema.InsertSupplier): Promise<schema.Supplier> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.suppliers);
    const supplierCount = Number(count[0].count) + 1;
    const code = `SUPP-${supplierCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.suppliers)
      .values({ ...supplier, code })
      .returning();
    return created;
  }

  async updateSupplier(id: number, supplier: Partial<schema.InsertSupplier>): Promise<schema.Supplier> {
    const [updated] = await db
      .update(schema.suppliers)
      .set(supplier)
      .where(eq(schema.suppliers.id, id))
      .returning();
    return updated;
  }

  async deleteSupplier(id: number): Promise<void> {
    await db.delete(schema.suppliers).where(eq(schema.suppliers.id, id));
  }

  async getServices(): Promise<schema.Service[]> {
    return await db.select().from(schema.services).orderBy(desc(schema.services.createdAt));
  }

  async getService(id: number): Promise<schema.Service | undefined> {
    const [service] = await db.select().from(schema.services).where(eq(schema.services.id, id));
    return service || undefined;
  }

  async createService(service: schema.InsertService): Promise<schema.Service> {
    const [created] = await db
      .insert(schema.services)
      .values(service)
      .returning();
    return created;
  }

  async updateService(id: number, service: Partial<schema.InsertService>): Promise<schema.Service> {
    const [updated] = await db
      .update(schema.services)
      .set(service)
      .where(eq(schema.services.id, id))
      .returning();
    return updated;
  }

  async deleteService(id: number): Promise<void> {
    await db.delete(schema.services).where(eq(schema.services.id, id));
  }

  async getSalesInvoices(): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]> {
    return await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .orderBy(desc(schema.salesInvoices.createdAt))
      .then(results => results.map(result => ({
        ...result.sales_invoices,
        customer: result.customers!
      })));
  }

  async getSalesInvoice(id: number): Promise<(schema.SalesInvoice & { customer: schema.Customer; items: (schema.SalesInvoiceItem & { service: schema.Service })[] }) | undefined> {
    const [invoice] = await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .where(eq(schema.salesInvoices.id, id));

    if (!invoice) return undefined;

    const items = await db
      .select()
      .from(schema.salesInvoiceItems)
      .leftJoin(schema.services, eq(schema.salesInvoiceItems.serviceId, schema.services.id))
      .where(eq(schema.salesInvoiceItems.invoiceId, id))
      .then(results => results.map(result => ({
        ...result.sales_invoice_items,
        service: result.services!
      })));

    return {
      ...invoice.sales_invoices,
      customer: invoice.customers!,
      items
    };
  }

  async createSalesInvoice(invoice: schema.InsertSalesInvoice, items: schema.InsertSalesInvoiceItem[]): Promise<schema.SalesInvoice> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.salesInvoices);
    const invoiceCount = Number(count[0].count) + 1;
    const invoiceNumber = `INV-${new Date().getFullYear()}-${invoiceCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.salesInvoices)
      .values({ ...invoice, invoiceNumber })
      .returning();

    // Insert items
    const itemsWithInvoiceId = items.map(item => ({ ...item, invoiceId: created.id }));
    await db.insert(schema.salesInvoiceItems).values(itemsWithInvoiceId);

    return created;
  }

  async updateSalesInvoice(id: number, invoice: Partial<schema.InsertSalesInvoice>): Promise<schema.SalesInvoice> {
    const [updated] = await db
      .update(schema.salesInvoices)
      .set({ ...invoice, updatedAt: new Date() })
      .where(eq(schema.salesInvoices.id, id))
      .returning();
    return updated;
  }

  async deleteSalesInvoice(id: number): Promise<void> {
    await db.delete(schema.salesInvoiceItems).where(eq(schema.salesInvoiceItems.invoiceId, id));
    await db.delete(schema.salesInvoices).where(eq(schema.salesInvoices.id, id));
  }

  async getEmployees(): Promise<schema.Employee[]> {
    return await db.select().from(schema.employees).orderBy(desc(schema.employees.createdAt));
  }

  async getEmployee(id: number): Promise<schema.Employee | undefined> {
    const [employee] = await db.select().from(schema.employees).where(eq(schema.employees.id, id));
    return employee || undefined;
  }

  async createEmployee(employee: schema.InsertEmployee): Promise<schema.Employee> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.employees);
    const employeeCount = Number(count[0].count) + 1;
    const code = `EMP-${employeeCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.employees)
      .values({ ...employee, code })
      .returning();
    return created;
  }

  async updateEmployee(id: number, employee: Partial<schema.InsertEmployee>): Promise<schema.Employee> {
    const [updated] = await db
      .update(schema.employees)
      .set(employee)
      .where(eq(schema.employees.id, id))
      .returning();
    return updated;
  }

  async deleteEmployee(id: number): Promise<void> {
    await db.delete(schema.employees).where(eq(schema.employees.id, id));
  }

  async getPayrollRecords(): Promise<(schema.PayrollRecord & { employee: schema.Employee })[]> {
    return await db
      .select()
      .from(schema.payrollRecords)
      .leftJoin(schema.employees, eq(schema.payrollRecords.employeeId, schema.employees.id))
      .orderBy(desc(schema.payrollRecords.createdAt))
      .then(results => results.map(result => ({
        ...result.payroll_records,
        employee: result.employees!
      })));
  }

  async getPayrollRecord(id: number): Promise<(schema.PayrollRecord & { employee: schema.Employee }) | undefined> {
    const [record] = await db
      .select()
      .from(schema.payrollRecords)
      .leftJoin(schema.employees, eq(schema.payrollRecords.employeeId, schema.employees.id))
      .where(eq(schema.payrollRecords.id, id));

    if (!record) return undefined;

    return {
      ...record.payroll_records,
      employee: record.employees!
    };
  }

  async createPayrollRecord(record: schema.InsertPayrollRecord): Promise<schema.PayrollRecord> {
    const [created] = await db
      .insert(schema.payrollRecords)
      .values(record)
      .returning();
    return created;
  }

  async updatePayrollRecord(id: number, record: Partial<schema.InsertPayrollRecord>): Promise<schema.PayrollRecord> {
    const [updated] = await db
      .update(schema.payrollRecords)
      .set(record)
      .where(eq(schema.payrollRecords.id, id))
      .returning();
    return updated;
  }

  async deletePayrollRecord(id: number): Promise<void> {
    await db.delete(schema.payrollRecords).where(eq(schema.payrollRecords.id, id));
  }

  async getWarehouses(): Promise<schema.Warehouse[]> {
    return await db.select().from(schema.warehouses).orderBy(desc(schema.warehouses.createdAt));
  }

  async getWarehouse(id: number): Promise<schema.Warehouse | undefined> {
    const [warehouse] = await db.select().from(schema.warehouses).where(eq(schema.warehouses.id, id));
    return warehouse || undefined;
  }

  async createWarehouse(warehouse: schema.InsertWarehouse): Promise<schema.Warehouse> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.warehouses);
    const warehouseCount = Number(count[0].count) + 1;
    const code = `WH-${warehouseCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.warehouses)
      .values({ ...warehouse, code })
      .returning();
    return created;
  }

  async updateWarehouse(id: number, warehouse: Partial<schema.InsertWarehouse>): Promise<schema.Warehouse> {
    const [updated] = await db
      .update(schema.warehouses)
      .set(warehouse)
      .where(eq(schema.warehouses.id, id))
      .returning();
    return updated;
  }

  async deleteWarehouse(id: number): Promise<void> {
    await db.delete(schema.warehouses).where(eq(schema.warehouses.id, id));
  }

  async getInventoryItems(): Promise<schema.InventoryItem[]> {
    return await db
      .select()
      .from(schema.inventoryItems)
      .orderBy(desc(schema.inventoryItems.createdAt));
  }

  async getInventoryItem(id: number): Promise<schema.InventoryItem | undefined> {
    const [item] = await db
      .select()
      .from(schema.inventoryItems)
      .where(eq(schema.inventoryItems.id, id));

    return item || undefined;
  }

  async createInventoryItem(item: schema.InsertInventoryItem): Promise<schema.InventoryItem> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.inventoryItems);
    const itemCount = Number(count[0].count) + 1;
    const code = `ITEM-${itemCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.inventoryItems)
      .values({ ...item, code })
      .returning();
    return created;
  }

  async updateInventoryItem(id: number, item: Partial<schema.InsertInventoryItem>): Promise<schema.InventoryItem> {
    const [updated] = await db
      .update(schema.inventoryItems)
      .set(item)
      .where(eq(schema.inventoryItems.id, id))
      .returning();
    return updated;
  }

  async deleteInventoryItem(id: number): Promise<void> {
    await db.delete(schema.inventoryItems).where(eq(schema.inventoryItems.id, id));
  }

  async getStockMovements(): Promise<(schema.StockMovement & { warehouse: schema.Warehouse; item: schema.InventoryItem })[]> {
    return await db
      .select()
      .from(schema.stockMovements)
      .leftJoin(schema.warehouses, eq(schema.stockMovements.warehouseId, schema.warehouses.id))
      .leftJoin(schema.inventoryItems, eq(schema.stockMovements.itemId, schema.inventoryItems.id))
      .orderBy(desc(schema.stockMovements.createdAt))
      .then(results => results.map(result => ({
        ...result.stock_movements,
        warehouse: result.warehouses!,
        item: result.inventory_items!
      })));
  }

  async createStockMovement(movement: schema.InsertStockMovement): Promise<schema.StockMovement> {
    const [created] = await db
      .insert(schema.stockMovements)
      .values(movement)
      .returning();
    return created;
  }

  async getDashboardStats() {
    // Get total sales for current month
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const salesResult = await db
      .select({ total: sql`COALESCE(SUM(total), 0)` })
      .from(schema.salesInvoices)
      .where(and(
        eq(schema.salesInvoices.status, 'paid'),
        gte(schema.salesInvoices.invoiceDate, Math.floor(currentMonth.getTime() / 1000))
      ));

    // Get active orders count
    const ordersResult = await db
      .select({ count: sql`count(*)` })
      .from(schema.salesInvoices)
      .where(or(
        eq(schema.salesInvoices.status, 'draft'),
        eq(schema.salesInvoices.status, 'sent')
      ));

    // Get inventory value (simplified - just count items)
    const inventoryResult = await db
      .select({ value: sql`COALESCE(SUM(selling_price * current_stock), 0)` })
      .from(schema.inventoryItems);

    // Get employees count
    const employeesResult = await db
      .select({ count: sql`count(*)` })
      .from(schema.employees)
      .where(eq(schema.employees.active, 1));

    return {
      totalSales: salesResult[0].total?.toString() || '0',
      activeOrders: Number(ordersResult[0].count),
      inventoryValue: inventoryResult[0].value?.toString() || '0',
      employees: Number(employeesResult[0].count)
    };
  }

  async getRecentSalesInvoices(limit = 5): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]> {
    return await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .orderBy(desc(schema.salesInvoices.createdAt))
      .limit(limit)
      .then(results => results.map(result => ({
        ...result.sales_invoices,
        customer: result.customers!
      })));
  }
}

export const storage = new DatabaseStorage();
