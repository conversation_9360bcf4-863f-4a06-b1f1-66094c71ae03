import { db } from "./db";
import { eq, desc, and, or, sql, like, gte, lte } from "drizzle-orm";
import * as schema from "@shared/schema";

export interface IStorage {
  // Users
  getUser(id: number): Promise<schema.User | undefined>;
  getUserByUsername(username: string): Promise<schema.User | undefined>;
  createUser(user: schema.InsertUser): Promise<schema.User>;

  // Company
  getCompany(): Promise<schema.Company | undefined>;
  createOrUpdateCompany(company: schema.InsertCompany): Promise<schema.Company>;

  // Customers
  getCustomers(): Promise<schema.Customer[]>;
  getCustomer(id: number): Promise<schema.Customer | undefined>;
  createCustomer(customer: schema.InsertCustomer): Promise<schema.Customer>;
  updateCustomer(id: number, customer: Partial<schema.InsertCustomer>): Promise<schema.Customer>;
  deleteCustomer(id: number): Promise<void>;

  // Suppliers
  getSuppliers(): Promise<schema.Supplier[]>;
  getSupplier(id: number): Promise<schema.Supplier | undefined>;
  createSupplier(supplier: schema.InsertSupplier): Promise<schema.Supplier>;
  updateSupplier(id: number, supplier: Partial<schema.InsertSupplier>): Promise<schema.Supplier>;
  deleteSupplier(id: number): Promise<void>;

  // Services
  getServices(): Promise<schema.Service[]>;
  getService(id: number): Promise<schema.Service | undefined>;
  createService(service: schema.InsertService): Promise<schema.Service>;
  updateService(id: number, service: Partial<schema.InsertService>): Promise<schema.Service>;
  deleteService(id: number): Promise<void>;

  // Sales Invoices
  getSalesInvoices(): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]>;
  getSalesInvoice(id: number): Promise<(schema.SalesInvoice & { customer: schema.Customer; items: (schema.SalesInvoiceItem & { service: schema.Service })[] }) | undefined>;
  createSalesInvoice(invoice: schema.InsertSalesInvoice, items: schema.InsertSalesInvoiceItem[]): Promise<schema.SalesInvoice>;
  updateSalesInvoice(id: number, invoice: Partial<schema.InsertSalesInvoice>): Promise<schema.SalesInvoice>;
  deleteSalesInvoice(id: number): Promise<void>;

  // Employees
  getEmployees(): Promise<schema.Employee[]>;
  getEmployee(id: number): Promise<schema.Employee | undefined>;
  createEmployee(employee: schema.InsertEmployee): Promise<schema.Employee>;
  updateEmployee(id: number, employee: Partial<schema.InsertEmployee>): Promise<schema.Employee>;
  deleteEmployee(id: number): Promise<void>;

  // Payroll
  getPayrollRecords(): Promise<(schema.PayrollRecord & { employee: schema.Employee })[]>;
  getPayrollRecord(id: number): Promise<(schema.PayrollRecord & { employee: schema.Employee }) | undefined>;
  createPayrollRecord(record: schema.InsertPayrollRecord): Promise<schema.PayrollRecord>;
  updatePayrollRecord(id: number, record: Partial<schema.InsertPayrollRecord>): Promise<schema.PayrollRecord>;
  deletePayrollRecord(id: number): Promise<void>;

  // Warehouses
  getWarehouses(): Promise<schema.Warehouse[]>;
  getWarehouse(id: number): Promise<schema.Warehouse | undefined>;
  createWarehouse(warehouse: schema.InsertWarehouse): Promise<schema.Warehouse>;
  updateWarehouse(id: number, warehouse: Partial<schema.InsertWarehouse>): Promise<schema.Warehouse>;
  deleteWarehouse(id: number): Promise<void>;

  // Inventory Items
  getInventoryItems(): Promise<schema.InventoryItem[]>;
  getInventoryItem(id: number): Promise<schema.InventoryItem | undefined>;
  createInventoryItem(item: schema.InsertInventoryItem): Promise<schema.InventoryItem>;
  updateInventoryItem(id: number, item: Partial<schema.InsertInventoryItem>): Promise<schema.InventoryItem>;
  deleteInventoryItem(id: number): Promise<void>;

  // Stock Movements
  getStockMovements(): Promise<(schema.StockMovement & { warehouse: schema.Warehouse; item: schema.InventoryItem })[]>;
  createStockMovement(movement: schema.InsertStockMovement): Promise<schema.StockMovement>;

  // Dashboard Stats
  getDashboardStats(): Promise<{
    totalSales: string;
    activeOrders: number;
    inventoryValue: string;
    employees: number;
  }>;

  // Recent invoices for dashboard
  getRecentSalesInvoices(limit?: number): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]>;

  // Payroll Processing
  processPayroll(data: {
    employeeIds: number[];
    payPeriodStart: Date;
    payPeriodEnd: Date;
    overtimeRates?: Record<number, number>;
    bonuses?: Record<number, number>;
    deductions?: Record<number, number>;
  }): Promise<schema.PayrollRecord[]>;

  // Payroll Reports
  getPayrollSummary(from?: string, to?: string): Promise<{
    totalEmployees: number;
    totalGrossPay: number;
    totalNetPay: number;
    totalOvertime: number;
    totalBonuses: number;
    totalDeductions: number;
    averageSalary: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<schema.User | undefined> {
    const [user] = await db.select().from(schema.users).where(eq(schema.users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<schema.User | undefined> {
    const [user] = await db.select().from(schema.users).where(eq(schema.users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: schema.InsertUser): Promise<schema.User> {
    const [user] = await db
      .insert(schema.users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getCompany(): Promise<schema.Company | undefined> {
    const [company] = await db.select().from(schema.companies).limit(1);
    return company || undefined;
  }

  async createOrUpdateCompany(company: schema.InsertCompany): Promise<schema.Company> {
    const existing = await this.getCompany();
    if (existing) {
      const [updated] = await db
        .update(schema.companies)
        .set(company)
        .where(eq(schema.companies.id, existing.id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(schema.companies)
        .values(company)
        .returning();
      return created;
    }
  }

  async getCustomers(): Promise<schema.Customer[]> {
    return await db.select().from(schema.customers).orderBy(desc(schema.customers.createdAt));
  }

  async getCustomer(id: number): Promise<schema.Customer | undefined> {
    const [customer] = await db.select().from(schema.customers).where(eq(schema.customers.id, id));
    return customer || undefined;
  }

  async createCustomer(customer: schema.InsertCustomer): Promise<schema.Customer> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.customers);
    const customerCount = Number(count[0].count) + 1;
    const code = `CUST-${customerCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.customers)
      .values({ ...customer, code })
      .returning();
    return created;
  }

  async updateCustomer(id: number, customer: Partial<schema.InsertCustomer>): Promise<schema.Customer> {
    const [updated] = await db
      .update(schema.customers)
      .set(customer)
      .where(eq(schema.customers.id, id))
      .returning();
    return updated;
  }

  async deleteCustomer(id: number): Promise<void> {
    await db.delete(schema.customers).where(eq(schema.customers.id, id));
  }

  async getSuppliers(): Promise<schema.Supplier[]> {
    return await db.select().from(schema.suppliers).orderBy(desc(schema.suppliers.createdAt));
  }

  async getSupplier(id: number): Promise<schema.Supplier | undefined> {
    const [supplier] = await db.select().from(schema.suppliers).where(eq(schema.suppliers.id, id));
    return supplier || undefined;
  }

  async createSupplier(supplier: schema.InsertSupplier): Promise<schema.Supplier> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.suppliers);
    const supplierCount = Number(count[0].count) + 1;
    const code = `SUPP-${supplierCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.suppliers)
      .values({ ...supplier, code })
      .returning();
    return created;
  }

  async updateSupplier(id: number, supplier: Partial<schema.InsertSupplier>): Promise<schema.Supplier> {
    const [updated] = await db
      .update(schema.suppliers)
      .set(supplier)
      .where(eq(schema.suppliers.id, id))
      .returning();
    return updated;
  }

  async deleteSupplier(id: number): Promise<void> {
    await db.delete(schema.suppliers).where(eq(schema.suppliers.id, id));
  }

  async getServices(): Promise<schema.Service[]> {
    return await db.select().from(schema.services).orderBy(desc(schema.services.createdAt));
  }

  async getService(id: number): Promise<schema.Service | undefined> {
    const [service] = await db.select().from(schema.services).where(eq(schema.services.id, id));
    return service || undefined;
  }

  async createService(service: schema.InsertService): Promise<schema.Service> {
    const [created] = await db
      .insert(schema.services)
      .values(service)
      .returning();
    return created;
  }

  async updateService(id: number, service: Partial<schema.InsertService>): Promise<schema.Service> {
    const [updated] = await db
      .update(schema.services)
      .set(service)
      .where(eq(schema.services.id, id))
      .returning();
    return updated;
  }

  async deleteService(id: number): Promise<void> {
    await db.delete(schema.services).where(eq(schema.services.id, id));
  }

  async getSalesInvoices(): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]> {
    return await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .orderBy(desc(schema.salesInvoices.createdAt))
      .then(results => results.map(result => ({
        ...result.sales_invoices,
        customer: result.customers!
      })));
  }

  async getSalesInvoice(id: number): Promise<(schema.SalesInvoice & { customer: schema.Customer; items: (schema.SalesInvoiceItem & { service: schema.Service })[] }) | undefined> {
    const [invoice] = await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .where(eq(schema.salesInvoices.id, id));

    if (!invoice) return undefined;

    const items = await db
      .select()
      .from(schema.salesInvoiceItems)
      .leftJoin(schema.services, eq(schema.salesInvoiceItems.serviceId, schema.services.id))
      .where(eq(schema.salesInvoiceItems.invoiceId, id))
      .then(results => results.map(result => ({
        ...result.sales_invoice_items,
        service: result.services!
      })));

    return {
      ...invoice.sales_invoices,
      customer: invoice.customers!,
      items
    };
  }

  async createSalesInvoice(invoice: schema.InsertSalesInvoice, items: schema.InsertSalesInvoiceItem[]): Promise<schema.SalesInvoice> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.salesInvoices);
    const invoiceCount = Number(count[0].count) + 1;
    const invoiceNumber = `INV-${new Date().getFullYear()}-${invoiceCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.salesInvoices)
      .values({ ...invoice, invoiceNumber })
      .returning();

    // Insert items
    const itemsWithInvoiceId = items.map(item => ({ ...item, invoiceId: created.id }));
    await db.insert(schema.salesInvoiceItems).values(itemsWithInvoiceId);

    return created;
  }

  async updateSalesInvoice(id: number, invoice: Partial<schema.InsertSalesInvoice>): Promise<schema.SalesInvoice> {
    const [updated] = await db
      .update(schema.salesInvoices)
      .set({ ...invoice, updatedAt: new Date() })
      .where(eq(schema.salesInvoices.id, id))
      .returning();
    return updated;
  }

  async deleteSalesInvoice(id: number): Promise<void> {
    await db.delete(schema.salesInvoiceItems).where(eq(schema.salesInvoiceItems.invoiceId, id));
    await db.delete(schema.salesInvoices).where(eq(schema.salesInvoices.id, id));
  }

  async getEmployees(): Promise<schema.Employee[]> {
    return await db.select().from(schema.employees).orderBy(desc(schema.employees.createdAt));
  }

  async getEmployee(id: number): Promise<schema.Employee | undefined> {
    const [employee] = await db.select().from(schema.employees).where(eq(schema.employees.id, id));
    return employee || undefined;
  }

  async createEmployee(employee: schema.InsertEmployee): Promise<schema.Employee> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.employees);
    const employeeCount = Number(count[0].count) + 1;
    const code = `EMP-${employeeCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.employees)
      .values({ ...employee, code })
      .returning();
    return created;
  }

  async updateEmployee(id: number, employee: Partial<schema.InsertEmployee>): Promise<schema.Employee> {
    const [updated] = await db
      .update(schema.employees)
      .set(employee)
      .where(eq(schema.employees.id, id))
      .returning();
    return updated;
  }

  async deleteEmployee(id: number): Promise<void> {
    await db.delete(schema.employees).where(eq(schema.employees.id, id));
  }

  async getPayrollRecords(): Promise<(schema.PayrollRecord & { employee: schema.Employee })[]> {
    return await db
      .select()
      .from(schema.payrollRecords)
      .leftJoin(schema.employees, eq(schema.payrollRecords.employeeId, schema.employees.id))
      .orderBy(desc(schema.payrollRecords.createdAt))
      .then(results => results.map(result => ({
        ...result.payroll_records,
        employee: result.employees!
      })));
  }

  async getPayrollRecord(id: number): Promise<(schema.PayrollRecord & { employee: schema.Employee }) | undefined> {
    const [record] = await db
      .select()
      .from(schema.payrollRecords)
      .leftJoin(schema.employees, eq(schema.payrollRecords.employeeId, schema.employees.id))
      .where(eq(schema.payrollRecords.id, id));

    if (!record) return undefined;

    return {
      ...record.payroll_records,
      employee: record.employees!
    };
  }

  async createPayrollRecord(record: schema.InsertPayrollRecord): Promise<schema.PayrollRecord> {
    const [created] = await db
      .insert(schema.payrollRecords)
      .values(record)
      .returning();
    return created;
  }

  async updatePayrollRecord(id: number, record: Partial<schema.InsertPayrollRecord>): Promise<schema.PayrollRecord> {
    const [updated] = await db
      .update(schema.payrollRecords)
      .set(record)
      .where(eq(schema.payrollRecords.id, id))
      .returning();
    return updated;
  }

  async deletePayrollRecord(id: number): Promise<void> {
    await db.delete(schema.payrollRecords).where(eq(schema.payrollRecords.id, id));
  }

  async getWarehouses(): Promise<schema.Warehouse[]> {
    return await db.select().from(schema.warehouses).orderBy(desc(schema.warehouses.createdAt));
  }

  async getWarehouse(id: number): Promise<schema.Warehouse | undefined> {
    const [warehouse] = await db.select().from(schema.warehouses).where(eq(schema.warehouses.id, id));
    return warehouse || undefined;
  }

  async createWarehouse(warehouse: schema.InsertWarehouse): Promise<schema.Warehouse> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.warehouses);
    const warehouseCount = Number(count[0].count) + 1;
    const code = `WH-${warehouseCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.warehouses)
      .values({ ...warehouse, code })
      .returning();
    return created;
  }

  async updateWarehouse(id: number, warehouse: Partial<schema.InsertWarehouse>): Promise<schema.Warehouse> {
    const [updated] = await db
      .update(schema.warehouses)
      .set(warehouse)
      .where(eq(schema.warehouses.id, id))
      .returning();
    return updated;
  }

  async deleteWarehouse(id: number): Promise<void> {
    await db.delete(schema.warehouses).where(eq(schema.warehouses.id, id));
  }

  async getInventoryItems(): Promise<schema.InventoryItem[]> {
    return await db
      .select()
      .from(schema.inventoryItems)
      .orderBy(desc(schema.inventoryItems.createdAt));
  }

  async getInventoryItem(id: number): Promise<schema.InventoryItem | undefined> {
    const [item] = await db
      .select()
      .from(schema.inventoryItems)
      .where(eq(schema.inventoryItems.id, id));

    return item || undefined;
  }

  async createInventoryItem(item: schema.InsertInventoryItem): Promise<schema.InventoryItem> {
    const count = await db.select({ count: sql`count(*)` }).from(schema.inventoryItems);
    const itemCount = Number(count[0].count) + 1;
    const code = `ITEM-${itemCount.toString().padStart(4, '0')}`;
    
    const [created] = await db
      .insert(schema.inventoryItems)
      .values({ ...item, code })
      .returning();
    return created;
  }

  async updateInventoryItem(id: number, item: Partial<schema.InsertInventoryItem>): Promise<schema.InventoryItem> {
    const [updated] = await db
      .update(schema.inventoryItems)
      .set(item)
      .where(eq(schema.inventoryItems.id, id))
      .returning();
    return updated;
  }

  async deleteInventoryItem(id: number): Promise<void> {
    await db.delete(schema.inventoryItems).where(eq(schema.inventoryItems.id, id));
  }

  async getStockMovements(): Promise<(schema.StockMovement & { warehouse: schema.Warehouse; item: schema.InventoryItem })[]> {
    return await db
      .select()
      .from(schema.stockMovements)
      .leftJoin(schema.warehouses, eq(schema.stockMovements.warehouseId, schema.warehouses.id))
      .leftJoin(schema.inventoryItems, eq(schema.stockMovements.itemId, schema.inventoryItems.id))
      .orderBy(desc(schema.stockMovements.createdAt))
      .then(results => results.map(result => ({
        ...result.stock_movements,
        warehouse: result.warehouses!,
        item: result.inventory_items!
      })));
  }

  async createStockMovement(movement: schema.InsertStockMovement): Promise<schema.StockMovement> {
    const [created] = await db
      .insert(schema.stockMovements)
      .values(movement)
      .returning();
    return created;
  }

  async getDashboardStats() {
    try {
      // Return simple stats for now
      return {
        totalSales: '0',
        activeOrders: 0,
        inventoryValue: '0',
        employees: 0
      };
    } catch (error) {
      console.error("Error in getDashboardStats:", error);
      // Return default values if there's an error
      return {
        totalSales: '0',
        activeOrders: 0,
        inventoryValue: '0',
        employees: 0
      };
    }
  }

  async getRecentSalesInvoices(limit = 5): Promise<(schema.SalesInvoice & { customer: schema.Customer })[]> {
    return await db
      .select()
      .from(schema.salesInvoices)
      .leftJoin(schema.customers, eq(schema.salesInvoices.customerId, schema.customers.id))
      .orderBy(desc(schema.salesInvoices.createdAt))
      .limit(limit)
      .then(results => results.map(result => ({
        ...result.sales_invoices,
        customer: result.customers!
      })));
  }

  async processPayroll(data: {
    employeeIds: number[];
    payPeriodStart: Date;
    payPeriodEnd: Date;
    overtimeRates?: Record<number, number>;
    bonuses?: Record<number, number>;
    deductions?: Record<number, number>;
  }): Promise<schema.PayrollRecord[]> {
    const processedRecords: schema.PayrollRecord[] = [];

    for (const employeeId of data.employeeIds) {
      const employee = await this.getEmployee(employeeId);
      if (!employee || !employee.active) continue;

      // Calculate basic pay for the period
      const daysInPeriod = Math.ceil((data.payPeriodEnd.getTime() - data.payPeriodStart.getTime()) / (1000 * 60 * 60 * 24));
      const workingDays = Math.min(daysInPeriod, 30); // Assume 30 working days max
      const basicPay = (employee.basicSalary || 0) * (workingDays / 30);

      // Calculate overtime
      const overtimeRate = data.overtimeRates?.[employeeId] || 0;
      const overtime = basicPay * (overtimeRate / 100);

      // Get bonuses and deductions
      const bonus = data.bonuses?.[employeeId] || 0;
      const deduction = data.deductions?.[employeeId] || 0;

      // Calculate gross and net pay
      const grossPay = basicPay + overtime + bonus;
      const netPay = grossPay - deduction;

      const payrollRecord: schema.InsertPayrollRecord = {
        employeeId,
        payPeriodStart: data.payPeriodStart,
        payPeriodEnd: data.payPeriodEnd,
        basicPay,
        overtime,
        bonuses: bonus,
        deductions: deduction,
        grossPay,
        netPay,
        status: 'pending'
      };

      const [created] = await db
        .insert(schema.payrollRecords)
        .values(payrollRecord)
        .returning();

      processedRecords.push(created);
    }

    return processedRecords;
  }

  async getPayrollSummary(from?: string, to?: string): Promise<{
    totalEmployees: number;
    totalGrossPay: number;
    totalNetPay: number;
    totalOvertime: number;
    totalBonuses: number;
    totalDeductions: number;
    averageSalary: number;
  }> {
    let query = db.select({
      totalEmployees: sql<number>`count(distinct ${schema.payrollRecords.employeeId})`,
      totalGrossPay: sql<number>`sum(${schema.payrollRecords.grossPay})`,
      totalNetPay: sql<number>`sum(${schema.payrollRecords.netPay})`,
      totalOvertime: sql<number>`sum(${schema.payrollRecords.overtime})`,
      totalBonuses: sql<number>`sum(${schema.payrollRecords.bonuses})`,
      totalDeductions: sql<number>`sum(${schema.payrollRecords.deductions})`,
    }).from(schema.payrollRecords);

    if (from && to) {
      query = query.where(
        and(
          gte(schema.payrollRecords.payPeriodStart, new Date(from)),
          lte(schema.payrollRecords.payPeriodEnd, new Date(to))
        )
      );
    }

    const [result] = await query;

    const totalEmployees = result.totalEmployees || 0;
    const averageSalary = totalEmployees > 0 ? (result.totalNetPay || 0) / totalEmployees : 0;

    return {
      totalEmployees,
      totalGrossPay: result.totalGrossPay || 0,
      totalNetPay: result.totalNetPay || 0,
      totalOvertime: result.totalOvertime || 0,
      totalBonuses: result.totalBonuses || 0,
      totalDeductions: result.totalDeductions || 0,
      averageSalary
    };
  }

  // Sales Reports
  async getSalesReports(from?: string, to?: string) {
    try {
      // For now, return mock data
      return {
        summary: {
          totalSales: 0,
          totalInvoices: 0,
          averageOrderValue: 0,
          totalCustomers: 0
        },
        monthlyTrend: [],
        topCustomers: [],
        topServices: [],
        paymentStatus: {
          paid: 0,
          pending: 0,
          overdue: 0
        }
      };
    } catch (error) {
      console.error("Error fetching sales reports:", error);
      throw error;
    }
  }

  // Payment Tracking
  async getPayments() {
    try {
      // For now, return mock data
      return [];
    } catch (error) {
      console.error("Error fetching payments:", error);
      throw error;
    }
  }

  async recordPayment(paymentData: any) {
    try {
      // For now, return mock data
      return { id: 1, ...paymentData };
    } catch (error) {
      console.error("Error recording payment:", error);
      throw error;
    }
  }

  // Sales Settings
  async getSalesSettings() {
    try {
      // For now, return default settings
      return {
        taxRate: 15,
        defaultPaymentTerms: 30,
        invoicePrefix: "INV",
        invoiceFooter: "Thank you for your business!",
        companyInfo: {
          name: "Glass Factory ERP",
          address: "",
          phone: "",
          email: "",
          taxId: ""
        },
        accountingSettings: {
          salesAccountId: 0,
          taxAccountId: 0,
          receivablesAccountId: 0
        },
        invoiceTemplate: "standard",
        autoGenerateInvoiceNumbers: true,
        requireCustomerApproval: false,
        sendEmailNotifications: true
      };
    } catch (error) {
      console.error("Error fetching sales settings:", error);
      throw error;
    }
  }

  async updateSalesSettings(settingsData: any) {
    try {
      // For now, return the updated settings
      return { id: 1, ...settingsData };
    } catch (error) {
      console.error("Error updating sales settings:", error);
      throw error;
    }
  }
}

export const storage = new DatabaseStorage();
