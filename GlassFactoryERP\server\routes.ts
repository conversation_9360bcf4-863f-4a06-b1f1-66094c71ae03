import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import * as schema from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  // Company routes
  app.get("/api/company", async (req, res) => {
    try {
      const company = await storage.getCompany();
      res.json(company);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch company" });
    }
  });

  app.post("/api/company", async (req, res) => {
    try {
      const data = schema.insertCompanySchema.parse(req.body);
      const company = await storage.createOrUpdateCompany(data);
      res.json(company);
    } catch (error) {
      res.status(400).json({ error: "Invalid company data" });
    }
  });

  // Dashboard stats
  app.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      console.error("Dashboard stats error:", error);
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });

  app.get("/api/dashboard/recent-invoices", async (req, res) => {
    try {
      const invoices = await storage.getRecentSalesInvoices(5);
      res.json(invoices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch recent invoices" });
    }
  });

  // Customer routes
  app.get("/api/customers", async (req, res) => {
    try {
      const customers = await storage.getCustomers();
      res.json(customers);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch customers" });
    }
  });

  app.get("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const customer = await storage.getCustomer(id);
      if (!customer) {
        return res.status(404).json({ error: "Customer not found" });
      }
      res.json(customer);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch customer" });
    }
  });

  app.post("/api/customers", async (req, res) => {
    try {
      const data = schema.insertCustomerSchema.parse(req.body);
      const customer = await storage.createCustomer(data);
      res.json(customer);
    } catch (error) {
      res.status(400).json({ error: "Invalid customer data" });
    }
  });

  app.put("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = schema.insertCustomerSchema.partial().parse(req.body);
      const customer = await storage.updateCustomer(id, data);
      res.json(customer);
    } catch (error) {
      res.status(400).json({ error: "Invalid customer data" });
    }
  });

  app.delete("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteCustomer(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete customer" });
    }
  });

  // Service routes
  app.get("/api/services", async (req, res) => {
    try {
      const services = await storage.getServices();
      res.json(services);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch services" });
    }
  });

  app.post("/api/services", async (req, res) => {
    try {
      const data = schema.insertServiceSchema.parse(req.body);
      const service = await storage.createService(data);
      res.json(service);
    } catch (error) {
      res.status(400).json({ error: "Invalid service data" });
    }
  });

  app.put("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = schema.insertServiceSchema.partial().parse(req.body);
      const service = await storage.updateService(id, data);
      res.json(service);
    } catch (error) {
      res.status(400).json({ error: "Invalid service data" });
    }
  });

  app.delete("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteService(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete service" });
    }
  });

  // Sales Invoice routes
  app.get("/api/sales/invoices", async (req, res) => {
    try {
      const invoices = await storage.getSalesInvoices();
      res.json(invoices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sales invoices" });
    }
  });

  app.get("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getSalesInvoice(id);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch invoice" });
    }
  });

  app.post("/api/sales/invoices", async (req, res) => {
    try {
      const { invoice, items } = req.body;
      const invoiceData = schema.insertSalesInvoiceSchema.parse(invoice);
      const itemsData = items.map((item: any) => schema.insertSalesInvoiceItemSchema.parse(item));
      
      const createdInvoice = await storage.createSalesInvoice(invoiceData, itemsData);
      res.json(createdInvoice);
    } catch (error) {
      res.status(400).json({ error: "Invalid invoice data" });
    }
  });

  app.put("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = schema.insertSalesInvoiceSchema.partial().parse(req.body);
      const invoice = await storage.updateSalesInvoice(id, data);
      res.json(invoice);
    } catch (error) {
      res.status(400).json({ error: "Invalid invoice data" });
    }
  });

  app.delete("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteSalesInvoice(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete invoice" });
    }
  });

  // Sales Reports routes
  app.get("/api/sales/reports", async (req, res) => {
    try {
      const { from, to } = req.query;
      const reports = await storage.getSalesReports(from as string, to as string);
      res.json(reports);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sales reports" });
    }
  });

  app.get("/api/sales/reports/export", async (req, res) => {
    try {
      const { from, to, format, type } = req.query;
      // For now, return a simple response
      res.json({ message: "Export functionality will be implemented" });
    } catch (error) {
      res.status(500).json({ error: "Failed to export report" });
    }
  });

  // Payment Tracking routes
  app.get("/api/payments", async (req, res) => {
    try {
      const payments = await storage.getPayments();
      res.json(payments);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch payments" });
    }
  });

  app.post("/api/payments/record", async (req, res) => {
    try {
      const data = req.body;
      const payment = await storage.recordPayment(data);
      res.json(payment);
    } catch (error) {
      res.status(400).json({ error: "Failed to record payment" });
    }
  });

  app.post("/api/payments/:id/reminder", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      // For now, return a simple response
      res.json({ message: "Reminder sent successfully" });
    } catch (error) {
      res.status(500).json({ error: "Failed to send reminder" });
    }
  });

  // Sales Settings routes
  app.get("/api/sales/settings", async (req, res) => {
    try {
      const settings = await storage.getSalesSettings();
      res.json(settings);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sales settings" });
    }
  });

  app.put("/api/sales/settings", async (req, res) => {
    try {
      const data = req.body;
      const settings = await storage.updateSalesSettings(data);
      res.json(settings);
    } catch (error) {
      res.status(400).json({ error: "Failed to update sales settings" });
    }
  });

  // Employee routes
  app.get("/api/employees", async (req, res) => {
    try {
      const employees = await storage.getEmployees();
      res.json(employees);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch employees" });
    }
  });

  app.post("/api/employees", async (req, res) => {
    try {
      const data = schema.insertEmployeeSchema.parse(req.body);
      const employee = await storage.createEmployee(data);
      res.json(employee);
    } catch (error) {
      res.status(400).json({ error: "Invalid employee data" });
    }
  });

  app.put("/api/employees/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = schema.insertEmployeeSchema.partial().parse(req.body);
      const employee = await storage.updateEmployee(id, data);
      res.json(employee);
    } catch (error) {
      res.status(400).json({ error: "Invalid employee data" });
    }
  });

  app.delete("/api/employees/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteEmployee(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete employee" });
    }
  });

  // Payroll routes
  app.get("/api/payroll", async (req, res) => {
    try {
      const payrollRecords = await storage.getPayrollRecords();
      res.json(payrollRecords);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch payroll records" });
    }
  });

  app.get("/api/payroll/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const record = await storage.getPayrollRecord(id);
      if (!record) {
        return res.status(404).json({ error: "Payroll record not found" });
      }
      res.json(record);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch payroll record" });
    }
  });

  app.post("/api/payroll", async (req, res) => {
    try {
      const data = schema.insertPayrollRecordSchema.parse(req.body);
      const record = await storage.createPayrollRecord(data);
      res.json(record);
    } catch (error) {
      res.status(400).json({ error: "Invalid payroll record data" });
    }
  });

  app.put("/api/payroll/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = schema.insertPayrollRecordSchema.partial().parse(req.body);
      const record = await storage.updatePayrollRecord(id, data);
      res.json(record);
    } catch (error) {
      res.status(400).json({ error: "Invalid payroll record data" });
    }
  });

  app.delete("/api/payroll/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deletePayrollRecord(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete payroll record" });
    }
  });

  app.post("/api/payroll/process", async (req, res) => {
    try {
      const { employeeIds, payPeriodStart, payPeriodEnd, overtimeRates, bonuses, deductions } = req.body;
      const processedRecords = await storage.processPayroll({
        employeeIds,
        payPeriodStart: new Date(payPeriodStart),
        payPeriodEnd: new Date(payPeriodEnd),
        overtimeRates,
        bonuses,
        deductions
      });
      res.json(processedRecords);
    } catch (error) {
      res.status(400).json({ error: "Failed to process payroll" });
    }
  });

  app.get("/api/payroll/reports/summary", async (req, res) => {
    try {
      const { from, to } = req.query;
      const summary = await storage.getPayrollSummary(from as string, to as string);
      res.json(summary);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch payroll summary" });
    }
  });

  // Warehouse routes
  app.get("/api/warehouses", async (req, res) => {
    try {
      const warehouses = await storage.getWarehouses();
      res.json(warehouses);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch warehouses" });
    }
  });

  app.post("/api/warehouses", async (req, res) => {
    try {
      const data = schema.insertWarehouseSchema.parse(req.body);
      const warehouse = await storage.createWarehouse(data);
      res.json(warehouse);
    } catch (error) {
      res.status(400).json({ error: "Invalid warehouse data" });
    }
  });

  // Inventory routes
  app.get("/api/inventory/items", async (req, res) => {
    try {
      const items = await storage.getInventoryItems();
      res.json(items);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch inventory items" });
    }
  });

  app.post("/api/inventory/items", async (req, res) => {
    try {
      const data = schema.insertInventoryItemSchema.parse(req.body);
      const item = await storage.createInventoryItem(data);
      res.json(item);
    } catch (error) {
      res.status(400).json({ error: "Invalid inventory item data" });
    }
  });

  app.get("/api/inventory/movements", async (req, res) => {
    try {
      const movements = await storage.getStockMovements();
      res.json(movements);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch stock movements" });
    }
  });

  app.post("/api/inventory/movements", async (req, res) => {
    try {
      const data = schema.insertStockMovementSchema.parse(req.body);
      const movement = await storage.createStockMovement(data);
      res.json(movement);
    } catch (error) {
      res.status(400).json({ error: "Invalid stock movement data" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
