import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import StatsCard from "@/components/ui/stats-card";
import SalesInvoiceModal from "@/components/modals/sales-invoice-modal";
import { useState } from "react";
import { 
  DollarSign, 
  ShoppingCart, 
  Package, 
  Users, 
  Plus, 
  Truck, 
  UserPlus, 
  BarChart3,
  Bell,
  User,
  ArrowRight
} from "lucide-react";

export default function Dashboard() {
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);

  const { data: stats } = useQuery({
    queryKey: ["/api/dashboard/stats"],
  });

  const { data: recentInvoices } = useQuery({
    queryKey: ["/api/dashboard/recent-invoices"],
  });

  const { data: company } = useQuery({
    queryKey: ["/api/company"],
  });

  const formatCurrency = (amount: string | number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(amount));
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'final':
        return 'bg-emerald-100 text-emerald-800';
      case 'draft':
        return 'bg-amber-100 text-amber-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      {/* Header */}
      <header className="bg-white border-b border-slate-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold text-slate-800">Dashboard</h2>
            <p className="text-slate-600 text-sm">Welcome back to your ERP system</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                3
              </span>
            </Button>
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-slate-800">John Admin</p>
                <p className="text-xs text-slate-600">System Administrator</p>
              </div>
              <div className="w-8 h-8 bg-slate-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-slate-600" />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Sales"
            value={formatCurrency(stats?.totalSales || 0)}
            change="+12.5% from last month"
            changeType="positive"
            icon={DollarSign}
            iconColor="emerald"
          />
          <StatsCard
            title="Active Orders"
            value={stats?.activeOrders?.toString() || "0"}
            subtitle="23 pending delivery"
            icon={ShoppingCart}
            iconColor="blue"
          />
          <StatsCard
            title="Inventory Value"
            value={formatCurrency(stats?.inventoryValue || 0)}
            subtitle="Across 5 warehouses"
            icon={Package}
            iconColor="purple"
          />
          <StatsCard
            title="Employees"
            value={stats?.employees?.toString() || "0"}
            subtitle="3 departments"
            icon={Users}
            iconColor="amber"
          />
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Sales Invoices */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Sales Invoices</CardTitle>
                  <Button variant="ghost" size="sm">
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-slate-50">
                      <tr>
                        <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Invoice #</th>
                        <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Customer</th>
                        <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Amount</th>
                        <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                        <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Date</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-200">
                      {recentInvoices?.length ? (
                        recentInvoices.map((invoice: any) => (
                          <tr key={invoice.id} className="hover:bg-slate-50">
                            <td className="px-6 py-4 text-sm font-medium text-slate-800">
                              {invoice.invoiceNumber}
                            </td>
                            <td className="px-6 py-4 text-sm text-slate-600">
                              {invoice.customer?.name}
                            </td>
                            <td className="px-6 py-4 text-sm font-medium text-slate-800">
                              {formatCurrency(invoice.total)}
                            </td>
                            <td className="px-6 py-4">
                              <Badge className={getStatusColor(invoice.status)}>
                                {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                              </Badge>
                            </td>
                            <td className="px-6 py-4 text-sm text-slate-600">
                              {formatDate(invoice.invoiceDate)}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="px-6 py-8 text-center text-slate-500">
                            No recent invoices found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions & Alerts */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  className="w-full justify-between bg-primary-50 text-primary-800 hover:bg-primary-100 border-primary-200"
                  variant="outline"
                  onClick={() => setShowInvoiceModal(true)}
                >
                  <div className="flex items-center space-x-3">
                    <Plus className="h-4 w-4" />
                    <span>New Sales Invoice</span>
                  </div>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button
                  className="w-full justify-between bg-emerald-50 text-emerald-800 hover:bg-emerald-100 border-emerald-200"
                  variant="outline"
                >
                  <div className="flex items-center space-x-3">
                    <Truck className="h-4 w-4" />
                    <span>Stock Receipt</span>
                  </div>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button
                  className="w-full justify-between bg-amber-50 text-amber-800 hover:bg-amber-100 border-amber-200"
                  variant="outline"
                >
                  <div className="flex items-center space-x-3">
                    <UserPlus className="h-4 w-4" />
                    <span>Add Employee</span>
                  </div>
                  <ArrowRight className="h-4 w-4" />
                </Button>
                
                <Button
                  className="w-full justify-between bg-purple-50 text-purple-800 hover:bg-purple-100 border-purple-200"
                  variant="outline"
                >
                  <div className="flex items-center space-x-3">
                    <BarChart3 className="h-4 w-4" />
                    <span>Generate Report</span>
                  </div>
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            {/* System Alerts */}
            <Card>
              <CardHeader>
                <CardTitle>System Alerts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3 p-3 bg-amber-50 rounded-lg">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-amber-800">Low Stock Alert</p>
                    <p className="text-xs text-amber-600">5 items below minimum stock level</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-blue-800">Pending Approvals</p>
                    <p className="text-xs text-blue-600">3 purchase orders awaiting approval</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3 p-3 bg-emerald-50 rounded-lg">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-emerald-800">Backup Complete</p>
                    <p className="text-xs text-emerald-600">Daily backup completed successfully</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      {/* Sales Invoice Modal */}
      <SalesInvoiceModal
        open={showInvoiceModal}
        onOpenChange={setShowInvoiceModal}
      />
    </>
  );
}
