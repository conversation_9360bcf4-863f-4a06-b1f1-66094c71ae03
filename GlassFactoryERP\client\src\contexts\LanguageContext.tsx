import React, { createContext, useContext, useEffect, useState } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  isRTL: boolean;
  t: (key: string) => string;
}

// Translation dictionaries
const translations = {
  en: {
    'nav.dashboard': 'Dashboard',
    'nav.companySettings': 'Company Settings',
    'nav.sales': 'Sales',
    'nav.purchases': 'Purchases',
    'nav.inventory': 'Inventory',
    'nav.payroll': 'Payroll',
    'nav.accounting': 'Accounting',
    'nav.manufacturing': 'Manufacturing',
    'nav.reports': 'Reports',
    'nav.main': 'Main',
    'nav.operations': 'Operations',
    'nav.humanResources': 'Human Resources',
    'nav.finance': 'Finance',
    'dashboard.title': 'Dashboard',
    'dashboard.subtitle': 'Welcome back to your ERP system',
    'dashboard.totalSales': 'Total Sales',
    'dashboard.activeOrders': 'Active Orders',
    'dashboard.inventoryValue': 'Inventory Value',
    'dashboard.employees': 'Employees',
    'dashboard.recentInvoices': 'Recent Sales Invoices',
    'dashboard.quickActions': 'Quick Actions',
    'dashboard.systemAlerts': 'System Alerts',
    'dashboard.newSalesInvoice': 'New Sales Invoice',
    'dashboard.stockReceipt': 'Stock Receipt',
    'dashboard.addEmployee': 'Add Employee',
    'dashboard.generateReport': 'Generate Report',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.loading': 'Loading...',
    'common.add': 'Add',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View'
  },
  ar: {
    'nav.dashboard': 'لوحة التحكم',
    'nav.companySettings': 'إعدادات الشركة',
    'nav.sales': 'المبيعات',
    'nav.purchases': 'المشتريات',
    'nav.inventory': 'المخزون',
    'nav.payroll': 'كشف المرتبات',
    'nav.accounting': 'المحاسبة',
    'nav.manufacturing': 'التصنيع',
    'nav.reports': 'التقارير',
    'nav.main': 'الرئيسية',
    'nav.operations': 'العمليات',
    'nav.humanResources': 'الموارد البشرية',
    'nav.finance': 'المالية',
    'dashboard.title': 'لوحة التحكم',
    'dashboard.subtitle': 'مرحباً بك في نظام تخطيط موارد المؤسسة',
    'dashboard.totalSales': 'إجمالي المبيعات',
    'dashboard.activeOrders': 'الطلبات النشطة',
    'dashboard.inventoryValue': 'قيمة المخزون',
    'dashboard.employees': 'الموظفون',
    'dashboard.recentInvoices': 'فواتير المبيعات الأخيرة',
    'dashboard.quickActions': 'الإجراءات السريعة',
    'dashboard.systemAlerts': 'تنبيهات النظام',
    'dashboard.newSalesInvoice': 'فاتورة مبيعات جديدة',
    'dashboard.stockReceipt': 'استلام مخزون',
    'dashboard.addEmployee': 'إضافة موظف',
    'dashboard.generateReport': 'إنشاء تقرير',
    'common.save': 'حفظ',
    'common.cancel': 'إلغاء',
    'common.loading': 'جاري التحميل...',
    'common.add': 'إضافة',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.view': 'عرض'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState('en');
  const [isRTL, setIsRTL] = useState(false);

  const setLanguage = (lang: string) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
  };

  const t = (key: string): string => {
    return translations[language as keyof typeof translations]?.[key as keyof typeof translations.en] || key;
  };

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && savedLanguage !== language) {
      setLanguageState(savedLanguage);
    }
  }, []);

  useEffect(() => {
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    const isRightToLeft = rtlLanguages.includes(language);
    setIsRTL(isRightToLeft);
    
    // Update document direction and language
    document.documentElement.dir = isRightToLeft ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    // Add RTL class to body for styling
    if (isRightToLeft) {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [language]);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL, t }}>
      {children}
    </LanguageContext.Provider>
  );
};