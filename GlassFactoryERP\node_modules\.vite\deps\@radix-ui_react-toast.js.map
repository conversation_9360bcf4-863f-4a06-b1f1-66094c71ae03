{"version": 3, "sources": ["../../@radix-ui/react-toast/src/toast.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { createContextScope } from '@radix-ui/react-context';\nimport * as DismissableLayer from '@radix-ui/react-dismissable-layer';\nimport { Portal } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToastProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'ToastProvider';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<ToastElement>('Toast');\n\ntype SwipeDirection = 'up' | 'down' | 'left' | 'right';\ntype ToastProviderContextValue = {\n  label: string;\n  duration: number;\n  swipeDirection: SwipeDirection;\n  swipeThreshold: number;\n  toastCount: number;\n  viewport: ToastViewportElement | null;\n  onViewportChange(viewport: ToastViewportElement): void;\n  onToastAdd(): void;\n  onToastRemove(): void;\n  isFocusedToastEscapeKeyDownRef: React.MutableRefObject<boolean>;\n  isClosePausedRef: React.MutableRefObject<boolean>;\n};\n\ntype ScopedProps<P> = P & { __scopeToast?: Scope };\nconst [createToastContext, createToastScope] = createContextScope('Toast', [createCollectionScope]);\nconst [ToastProviderProvider, useToastProviderContext] =\n  createToastContext<ToastProviderContextValue>(PROVIDER_NAME);\n\ninterface ToastProviderProps {\n  children?: React.ReactNode;\n  /**\n   * An author-localized label for each toast. Used to help screen reader users\n   * associate the interruption with a toast.\n   * @defaultValue 'Notification'\n   */\n  label?: string;\n  /**\n   * Time in milliseconds that each toast should remain visible for.\n   * @defaultValue 5000\n   */\n  duration?: number;\n  /**\n   * Direction of pointer swipe that should close the toast.\n   * @defaultValue 'right'\n   */\n  swipeDirection?: SwipeDirection;\n  /**\n   * Distance in pixels that the swipe must pass before a close is triggered.\n   * @defaultValue 50\n   */\n  swipeThreshold?: number;\n}\n\nconst ToastProvider: React.FC<ToastProviderProps> = (props: ScopedProps<ToastProviderProps>) => {\n  const {\n    __scopeToast,\n    label = 'Notification',\n    duration = 5000,\n    swipeDirection = 'right',\n    swipeThreshold = 50,\n    children,\n  } = props;\n  const [viewport, setViewport] = React.useState<ToastViewportElement | null>(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n\n  return (\n    <Collection.Provider scope={__scopeToast}>\n      <ToastProviderProvider\n        scope={__scopeToast}\n        label={label}\n        duration={duration}\n        swipeDirection={swipeDirection}\n        swipeThreshold={swipeThreshold}\n        toastCount={toastCount}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        onToastAdd={React.useCallback(() => setToastCount((prevCount) => prevCount + 1), [])}\n        onToastRemove={React.useCallback(() => setToastCount((prevCount) => prevCount - 1), [])}\n        isFocusedToastEscapeKeyDownRef={isFocusedToastEscapeKeyDownRef}\n        isClosePausedRef={isClosePausedRef}\n      >\n        {children}\n      </ToastProviderProvider>\n    </Collection.Provider>\n  );\n};\n\nToastProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ToastViewport';\nconst VIEWPORT_DEFAULT_HOTKEY = ['F8'];\nconst VIEWPORT_PAUSE = 'toast.viewportPause';\nconst VIEWPORT_RESUME = 'toast.viewportResume';\n\ntype ToastViewportElement = React.ElementRef<typeof Primitive.ol>;\ntype PrimitiveOrderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ol>;\ninterface ToastViewportProps extends PrimitiveOrderedListProps {\n  /**\n   * The keys to use as the keyboard shortcut that will move focus to the toast viewport.\n   * @defaultValue ['F8']\n   */\n  hotkey?: string[];\n  /**\n   * An author-localized label for the toast viewport to provide context for screen reader users\n   * when navigating page landmarks. The available `{hotkey}` placeholder will be replaced for you.\n   * @defaultValue 'Notifications ({hotkey})'\n   */\n  label?: string;\n}\n\nconst ToastViewport = React.forwardRef<ToastViewportElement, ToastViewportProps>(\n  (props: ScopedProps<ToastViewportProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = 'Notifications ({hotkey})',\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef<HTMLDivElement>(null);\n    const headFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const tailFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const ref = React.useRef<ToastViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const hasToasts = context.toastCount > 0;\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        // we use `event.code` as it is consistent regardless of meta keys that were pressed.\n        // for example, `event.key` for `Control+Alt+t` is `†` and `t !== †`\n        const isHotkeyPressed =\n          hotkey.length !== 0 && hotkey.every((key) => (event as any)[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }, [hotkey]);\n\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n\n        const handleFocusOutResume = (event: FocusEvent) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget as HTMLElement);\n          if (isFocusMovingOutside) handleResume();\n        };\n\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        wrapper.addEventListener('focusin', handlePause);\n        wrapper.addEventListener('focusout', handleFocusOutResume);\n        wrapper.addEventListener('pointermove', handlePause);\n        wrapper.addEventListener('pointerleave', handlePointerLeaveResume);\n        window.addEventListener('blur', handlePause);\n        window.addEventListener('focus', handleResume);\n        return () => {\n          wrapper.removeEventListener('focusin', handlePause);\n          wrapper.removeEventListener('focusout', handleFocusOutResume);\n          wrapper.removeEventListener('pointermove', handlePause);\n          wrapper.removeEventListener('pointerleave', handlePointerLeaveResume);\n          window.removeEventListener('blur', handlePause);\n          window.removeEventListener('focus', handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }: { tabbingDirection: 'forwards' | 'backwards' }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current!;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === 'forwards'\n            ? toastTabbableCandidates\n            : toastTabbableCandidates.reverse();\n        });\n        return (\n          tabbingDirection === 'forwards' ? tabbableCandidates.reverse() : tabbableCandidates\n        ).flat();\n      },\n      [getItems]\n    );\n\n    React.useEffect(() => {\n      const viewport = ref.current;\n      // We programmatically manage tabbing as we are unable to influence\n      // the source order with portals, this allows us to reverse the\n      // tab order so that it runs from most recent toast to least\n      if (viewport) {\n        const handleKeyDown = (event: KeyboardEvent) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n\n            // If we're back tabbing after jumping to the viewport then we simply\n            // proxy focus out to the preceding document\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n\n            const tabbingDirection = isTabbingBackwards ? 'backwards' : 'forwards';\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges so we\n              // proxy to the corresponding exit point and let the browser handle\n              // tab/shift+tab keypress and implicitly pass focus to the next valid element in the document\n              isTabbingBackwards\n                ? headFocusProxyRef.current?.focus()\n                : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        viewport.addEventListener('keydown', handleKeyDown);\n        return () => viewport.removeEventListener('keydown', handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n\n    return (\n      <DismissableLayer.Branch\n        ref={wrapperRef}\n        role=\"region\"\n        aria-label={label.replace('{hotkey}', hotkeyLabel)}\n        // Ensure virtual cursor from landmarks menus triggers focus/blur for pause/resume\n        tabIndex={-1}\n        // incase list has size when empty (e.g. padding), we remove pointer events so\n        // it doesn't prevent interactions with page elements that it overlays\n        style={{ pointerEvents: hasToasts ? undefined : 'none' }}\n      >\n        {hasToasts && (\n          <FocusProxy\n            ref={headFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'forwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n        {/**\n         * tabindex on the the list so that it can be focused when items are removed. we focus\n         * the list instead of the viewport so it announces number of items remaining.\n         */}\n        <Collection.Slot scope={__scopeToast}>\n          <Primitive.ol tabIndex={-1} {...viewportProps} ref={composedRefs} />\n        </Collection.Slot>\n        {hasToasts && (\n          <FocusProxy\n            ref={tailFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'backwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n      </DismissableLayer.Branch>\n    );\n  }\n);\n\nToastViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_PROXY_NAME = 'ToastFocusProxy';\n\ntype FocusProxyElement = React.ElementRef<typeof VisuallyHidden>;\ntype VisuallyHiddenProps = React.ComponentPropsWithoutRef<typeof VisuallyHidden>;\ninterface FocusProxyProps extends VisuallyHiddenProps {\n  onFocusFromOutsideViewport(): void;\n}\n\nconst FocusProxy = React.forwardRef<FocusProxyElement, ScopedProps<FocusProxyProps>>(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n\n    return (\n      <VisuallyHidden\n        aria-hidden\n        tabIndex={0}\n        {...proxyProps}\n        ref={forwardedRef}\n        // Avoid page scrolling when focus is on the focus proxy\n        style={{ position: 'fixed' }}\n        onFocus={(event) => {\n          const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }}\n      />\n    );\n  }\n);\n\nFocusProxy.displayName = FOCUS_PROXY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Toast\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOAST_NAME = 'Toast';\nconst TOAST_SWIPE_START = 'toast.swipeStart';\nconst TOAST_SWIPE_MOVE = 'toast.swipeMove';\nconst TOAST_SWIPE_CANCEL = 'toast.swipeCancel';\nconst TOAST_SWIPE_END = 'toast.swipeEnd';\n\ntype ToastElement = ToastImplElement;\ninterface ToastProps extends Omit<ToastImplProps, keyof ToastImplPrivateProps> {\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst Toast = React.forwardRef<ToastElement, ToastProps>(\n  (props: ScopedProps<ToastProps>, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open = true, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen,\n      onChange: onOpenChange,\n    });\n    return (\n      <Presence present={forceMount || open}>\n        <ToastImpl\n          open={open}\n          {...toastProps}\n          ref={forwardedRef}\n          onClose={() => setOpen(false)}\n          onPause={useCallbackRef(props.onPause)}\n          onResume={useCallbackRef(props.onResume)}\n          onSwipeStart={composeEventHandlers(props.onSwipeStart, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'start');\n          })}\n          onSwipeMove={composeEventHandlers(props.onSwipeMove, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'move');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-y', `${y}px`);\n          })}\n          onSwipeCancel={composeEventHandlers(props.onSwipeCancel, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'cancel');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-y');\n          })}\n          onSwipeEnd={composeEventHandlers(props.onSwipeEnd, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'end');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-y', `${y}px`);\n            setOpen(false);\n          })}\n        />\n      </Presence>\n    );\n  }\n);\n\nToast.displayName = TOAST_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype SwipeEvent = { currentTarget: EventTarget & ToastElement } & Omit<\n  CustomEvent<{ originalEvent: React.PointerEvent; delta: { x: number; y: number } }>,\n  'currentTarget'\n>;\n\nconst [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {},\n});\n\ntype ToastImplElement = React.ElementRef<typeof Primitive.li>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer.Root>;\ntype ToastImplPrivateProps = { open: boolean; onClose(): void };\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface ToastImplProps extends ToastImplPrivateProps, PrimitiveListItemProps {\n  type?: 'foreground' | 'background';\n  /**\n   * Time in milliseconds that toast should remain visible for. Overrides value\n   * given to `ToastProvider`.\n   */\n  duration?: number;\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPause?(): void;\n  onResume?(): void;\n  onSwipeStart?(event: SwipeEvent): void;\n  onSwipeMove?(event: SwipeEvent): void;\n  onSwipeCancel?(event: SwipeEvent): void;\n  onSwipeEnd?(event: SwipeEvent): void;\n}\n\nconst ToastImpl = React.forwardRef<ToastImplElement, ToastImplProps>(\n  (props: ScopedProps<ToastImplProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = 'foreground',\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState<ToastImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n    const swipeDeltaRef = React.useRef<{ x: number; y: number } | null>(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      // focus viewport if focus is within toast to read the remaining toast\n      // count to SR users and ensure focus isn't lost\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n\n    const startTimer = React.useCallback(\n      (duration: number) => {\n        if (!duration || duration === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration);\n      },\n      [handleClose]\n    );\n\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n\n    // start timer when toast opens or duration changes.\n    // we include `open` in deps because closed !== unmounted when animating\n    // so it could reopen before being completely unmounted\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n\n    if (!context.viewport) return null;\n\n    return (\n      <>\n        {announceTextContent && (\n          <ToastAnnounce\n            __scopeToast={__scopeToast}\n            // Toasts are always role=status to avoid stuttering issues with role=alert in SRs.\n            role=\"status\"\n            aria-live={type === 'foreground' ? 'assertive' : 'polite'}\n            aria-atomic\n          >\n            {announceTextContent}\n          </ToastAnnounce>\n        )}\n\n        <ToastInteractiveProvider scope={__scopeToast} onClose={handleClose}>\n          {ReactDOM.createPortal(\n            <Collection.ItemSlot scope={__scopeToast}>\n              <DismissableLayer.Root\n                asChild\n                onEscapeKeyDown={composeEventHandlers(onEscapeKeyDown, () => {\n                  if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                  context.isFocusedToastEscapeKeyDownRef.current = false;\n                })}\n              >\n                <Primitive.li\n                  // Ensure toasts are announced as status list or status when focused\n                  role=\"status\"\n                  aria-live=\"off\"\n                  aria-atomic\n                  tabIndex={0}\n                  data-state={open ? 'open' : 'closed'}\n                  data-swipe-direction={context.swipeDirection}\n                  {...toastProps}\n                  ref={composedRefs}\n                  style={{ userSelect: 'none', touchAction: 'none', ...props.style }}\n                  onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                    if (event.key !== 'Escape') return;\n                    onEscapeKeyDown?.(event.nativeEvent);\n                    if (!event.nativeEvent.defaultPrevented) {\n                      context.isFocusedToastEscapeKeyDownRef.current = true;\n                      handleClose();\n                    }\n                  })}\n                  onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                    if (event.button !== 0) return;\n                    pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                  })}\n                  onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n                    if (!pointerStartRef.current) return;\n                    const x = event.clientX - pointerStartRef.current.x;\n                    const y = event.clientY - pointerStartRef.current.y;\n                    const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                    const isHorizontalSwipe = ['left', 'right'].includes(context.swipeDirection);\n                    const clamp = ['left', 'up'].includes(context.swipeDirection)\n                      ? Math.min\n                      : Math.max;\n                    const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                    const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                    const moveStartBuffer = event.pointerType === 'touch' ? 10 : 2;\n                    const delta = { x: clampedX, y: clampedY };\n                    const eventDetail = { originalEvent: event, delta };\n                    if (hasSwipeMoveStarted) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                        discrete: false,\n                      });\n                    } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                        discrete: false,\n                      });\n                      (event.target as HTMLElement).setPointerCapture(event.pointerId);\n                    } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                      // User is swiping in wrong direction so we disable swipe gesture\n                      // for the current pointer down interaction\n                      pointerStartRef.current = null;\n                    }\n                  })}\n                  onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n                    const delta = swipeDeltaRef.current;\n                    const target = event.target as HTMLElement;\n                    if (target.hasPointerCapture(event.pointerId)) {\n                      target.releasePointerCapture(event.pointerId);\n                    }\n                    swipeDeltaRef.current = null;\n                    pointerStartRef.current = null;\n                    if (delta) {\n                      const toast = event.currentTarget;\n                      const eventDetail = { originalEvent: event, delta };\n                      if (\n                        isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)\n                      ) {\n                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                          discrete: true,\n                        });\n                      } else {\n                        handleAndDispatchCustomEvent(\n                          TOAST_SWIPE_CANCEL,\n                          onSwipeCancel,\n                          eventDetail,\n                          {\n                            discrete: true,\n                          }\n                        );\n                      }\n                      // Prevent click event from triggering on items within the toast when\n                      // pointer up is part of a swipe gesture\n                      toast.addEventListener('click', (event) => event.preventDefault(), {\n                        once: true,\n                      });\n                    }\n                  })}\n                />\n              </DismissableLayer.Root>\n            </Collection.ItemSlot>,\n            context.viewport\n          )}\n        </ToastInteractiveProvider>\n      </>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface ToastAnnounceProps\n  extends Omit<React.ComponentPropsWithoutRef<'div'>, 'children'>,\n    ScopedProps<{ children: string[] }> {}\n\nconst ToastAnnounce: React.FC<ToastAnnounceProps> = (props: ScopedProps<ToastAnnounceProps>) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n\n  // render text content in the next frame to ensure toast is announced in NVDA\n  useNextFrame(() => setRenderAnnounceText(true));\n\n  // cleanup after announcing\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1000);\n    return () => window.clearTimeout(timer);\n  }, []);\n\n  return isAnnounced ? null : (\n    <Portal asChild>\n      <VisuallyHidden {...announceProps}>\n        {renderAnnounceText && (\n          <>\n            {context.label} {children}\n          </>\n        )}\n      </VisuallyHidden>\n    </Portal>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ToastTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'ToastTitle';\n\ntype ToastTitleElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToastTitleProps extends PrimitiveDivProps {}\n\nconst ToastTitle = React.forwardRef<ToastTitleElement, ToastTitleProps>(\n  (props: ScopedProps<ToastTitleProps>, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return <Primitive.div {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nToastTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'ToastDescription';\n\ntype ToastDescriptionElement = React.ElementRef<typeof Primitive.div>;\ninterface ToastDescriptionProps extends PrimitiveDivProps {}\n\nconst ToastDescription = React.forwardRef<ToastDescriptionElement, ToastDescriptionProps>(\n  (props: ScopedProps<ToastDescriptionProps>, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return <Primitive.div {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nToastDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'ToastAction';\n\ntype ToastActionElement = ToastCloseElement;\ninterface ToastActionProps extends ToastCloseProps {\n  /**\n   * A short description for an alternate way to carry out the action. For screen reader users\n   * who will not be able to navigate to the button easily/quickly.\n   * @example <ToastAction altText=\"Goto account settings to upgrade\">Upgrade</ToastAction>\n   * @example <ToastAction altText=\"Undo (Alt+U)\">Undo</ToastAction>\n   */\n  altText: string;\n}\n\nconst ToastAction = React.forwardRef<ToastActionElement, ToastActionProps>(\n  (props: ScopedProps<ToastActionProps>, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n\n    return (\n      <ToastAnnounceExclude altText={altText} asChild>\n        <ToastClose {...actionProps} ref={forwardedRef} />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'ToastClose';\n\ntype ToastCloseElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToastCloseProps extends PrimitiveButtonProps {}\n\nconst ToastClose = React.forwardRef<ToastCloseElement, ToastCloseProps>(\n  (props: ScopedProps<ToastCloseProps>, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n\n    return (\n      <ToastAnnounceExclude asChild>\n        <Primitive.button\n          type=\"button\"\n          {...closeProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(props.onClick, interactiveContext.onClose)}\n        />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastClose.displayName = CLOSE_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype ToastAnnounceExcludeElement = React.ElementRef<typeof Primitive.div>;\ninterface ToastAnnounceExcludeProps extends PrimitiveDivProps {\n  altText?: string;\n}\n\nconst ToastAnnounceExclude = React.forwardRef<\n  ToastAnnounceExcludeElement,\n  ToastAnnounceExcludeProps\n>((props: ScopedProps<ToastAnnounceExcludeProps>, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n\n  return (\n    <Primitive.div\n      data-radix-toast-announce-exclude=\"\"\n      data-radix-toast-announce-alt={altText || undefined}\n      {...announceExcludeProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nfunction getAnnounceTextContent(container: HTMLElement) {\n  const textContent: string[] = [];\n  const childNodes = Array.from(container.childNodes);\n\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === 'none';\n      const isExcluded = node.dataset.radixToastAnnounceExclude === '';\n\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n\n  // We return a collection of text rather than a single concatenated string.\n  // This allows SR VO to naturally pause break between nodes while announcing.\n  return textContent;\n}\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction handleAndDispatchCustomEvent<\n  E extends CustomEvent,\n  ReactEvent extends React.SyntheticEvent,\n>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: ReactEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const currentTarget = detail.originalEvent.currentTarget as HTMLElement;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\n\nconst isDeltaInDirection = (\n  delta: { x: number; y: number },\n  direction: SwipeDirection,\n  threshold = 0\n) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === 'left' || direction === 'right') {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\n\nfunction useNextFrame(callback = () => {}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => (raf2 = window.requestAnimationFrame(fn)));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\n\nfunction isHTMLElement(node: any): node is HTMLElement {\n  return node.nodeType === node.ELEMENT_NODE;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nconst Provider = ToastProvider;\nconst Viewport = ToastViewport;\nconst Root = Toast;\nconst Title = ToastTitle;\nconst Description = ToastDescription;\nconst Action = ToastAction;\nconst Close = ToastClose;\n\nexport {\n  createToastScope,\n  //\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastAction,\n  ToastClose,\n  //\n  Provider,\n  Viewport,\n  Root,\n  Title,\n  Description,\n  Action,\n  Close,\n};\nexport type {\n  ToastProviderProps,\n  ToastViewportProps,\n  ToastProps,\n  ToastTitleProps,\n  ToastDescriptionProps,\n  ToastActionProps,\n  ToastCloseProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AACvB,eAA0B;AA2FpB,yBAAA;AAvEN,IAAM,gBAAgB;AAEtB,IAAM,CAAC,YAAY,eAAe,qBAAqB,IAAI,iBAA+B,OAAO;AAkBjG,IAAM,CAAC,oBAAoB,gBAAgB,IAAI,mBAAmB,SAAS,CAAC,qBAAqB,CAAC;AAClG,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,mBAA8C,aAAa;AA2B7D,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM;IACJ;IACA,QAAQ;IACR,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB;EACF,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,eAAsC,IAAI;AAChF,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,CAAC;AACpD,QAAM,iCAAuC,aAAO,KAAK;AACzD,QAAM,mBAAyB,aAAO,KAAK;AAE3C,MAAI,CAAC,MAAM,KAAK,GAAG;AACjB,YAAQ;MACN,wCAAwC,aAAa;IACvD;EACF;AAEA,aACE,wBAAC,WAAW,UAAX,EAAoB,OAAO,cAC1B,cAAA;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA;MACA;MACA;MACA;MACA,kBAAkB;MAClB,YAAkB,kBAAY,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC;MACnF,eAAqB,kBAAY,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC;MACtF;MACA;MAEC;IAAA;EACH,EAAA,CACF;AAEJ;AAEA,cAAc,cAAc;AAM5B,IAAM,gBAAgB;AACtB,IAAM,0BAA0B,CAAC,IAAI;AACrC,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAkBxB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM;MACJ;MACA,SAAS;MACT,QAAQ;MACR,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,wBAAwB,eAAe,YAAY;AACnE,UAAM,WAAW,cAAc,YAAY;AAC3C,UAAM,aAAmB,aAAuB,IAAI;AACpD,UAAM,oBAA0B,aAA0B,IAAI;AAC9D,UAAM,oBAA0B,aAA0B,IAAI;AAC9D,UAAM,MAAY,aAA6B,IAAI;AACnD,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,gBAAgB;AAChF,UAAM,cAAc,OAAO,KAAK,GAAG,EAAE,QAAQ,QAAQ,EAAE,EAAE,QAAQ,UAAU,EAAE;AAC7E,UAAM,YAAY,QAAQ,aAAa;AAEjC,IAAA,gBAAU,MAAM;AACpB,YAAM,gBAAgB,CAAC,UAAyB;;AAG9C,cAAM,kBACJ,OAAO,WAAW,KAAK,OAAO,MAAM,CAAC,QAAS,MAAc,GAAG,KAAK,MAAM,SAAS,GAAG;AACxF,YAAI,gBAAiB,WAAI,YAAJ,mBAAa;MACpC;AACA,eAAS,iBAAiB,WAAW,aAAa;AAClD,aAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;IACpE,GAAG,CAAC,MAAM,CAAC;AAEL,IAAA,gBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,YAAM,WAAW,IAAI;AACrB,UAAI,aAAa,WAAW,UAAU;AACpC,cAAM,cAAc,MAAM;AACxB,cAAI,CAAC,QAAQ,iBAAiB,SAAS;AACrC,kBAAM,aAAa,IAAI,YAAY,cAAc;AACjD,qBAAS,cAAc,UAAU;AACjC,oBAAQ,iBAAiB,UAAU;UACrC;QACF;AAEA,cAAM,eAAe,MAAM;AACzB,cAAI,QAAQ,iBAAiB,SAAS;AACpC,kBAAM,cAAc,IAAI,YAAY,eAAe;AACnD,qBAAS,cAAc,WAAW;AAClC,oBAAQ,iBAAiB,UAAU;UACrC;QACF;AAEA,cAAM,uBAAuB,CAAC,UAAsB;AAClD,gBAAM,uBAAuB,CAAC,QAAQ,SAAS,MAAM,aAA4B;AACjF,cAAI,qBAAsB,cAAa;QACzC;AAEA,cAAM,2BAA2B,MAAM;AACrC,gBAAM,gBAAgB,QAAQ,SAAS,SAAS,aAAa;AAC7D,cAAI,CAAC,cAAe,cAAa;QACnC;AAGA,gBAAQ,iBAAiB,WAAW,WAAW;AAC/C,gBAAQ,iBAAiB,YAAY,oBAAoB;AACzD,gBAAQ,iBAAiB,eAAe,WAAW;AACnD,gBAAQ,iBAAiB,gBAAgB,wBAAwB;AACjE,eAAO,iBAAiB,QAAQ,WAAW;AAC3C,eAAO,iBAAiB,SAAS,YAAY;AAC7C,eAAO,MAAM;AACX,kBAAQ,oBAAoB,WAAW,WAAW;AAClD,kBAAQ,oBAAoB,YAAY,oBAAoB;AAC5D,kBAAQ,oBAAoB,eAAe,WAAW;AACtD,kBAAQ,oBAAoB,gBAAgB,wBAAwB;AACpE,iBAAO,oBAAoB,QAAQ,WAAW;AAC9C,iBAAO,oBAAoB,SAAS,YAAY;QAClD;MACF;IACF,GAAG,CAAC,WAAW,QAAQ,gBAAgB,CAAC;AAExC,UAAM,8BAAoC;MACxC,CAAC,EAAE,iBAAiB,MAAsD;AACxE,cAAM,aAAa,SAAS;AAC5B,cAAM,qBAAqB,WAAW,IAAI,CAAC,cAAc;AACvD,gBAAM,YAAY,UAAU,IAAI;AAChC,gBAAM,0BAA0B,CAAC,WAAW,GAAG,sBAAsB,SAAS,CAAC;AAC/E,iBAAO,qBAAqB,aACxB,0BACA,wBAAwB,QAAQ;QACtC,CAAC;AACD,gBACE,qBAAqB,aAAa,mBAAmB,QAAQ,IAAI,oBACjE,KAAK;MACT;MACA,CAAC,QAAQ;IACX;AAEM,IAAA,gBAAU,MAAM;AACpB,YAAM,WAAW,IAAI;AAIrB,UAAI,UAAU;AACZ,cAAM,gBAAgB,CAAC,UAAyB;;AAC9C,gBAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;AACzD,gBAAM,WAAW,MAAM,QAAQ,SAAS,CAAC;AAEzC,cAAI,UAAU;AACZ,kBAAM,iBAAiB,SAAS;AAChC,kBAAM,qBAAqB,MAAM;AACjC,kBAAM,mBAAmB,MAAM,WAAW;AAI1C,gBAAI,oBAAoB,oBAAoB;AAC1C,sCAAkB,YAAlB,mBAA2B;AAC3B;YACF;AAEA,kBAAM,mBAAmB,qBAAqB,cAAc;AAC5D,kBAAM,mBAAmB,4BAA4B,EAAE,iBAAiB,CAAC;AACzE,kBAAM,QAAQ,iBAAiB,UAAU,CAAC,cAAc,cAAc,cAAc;AACpF,gBAAI,WAAW,iBAAiB,MAAM,QAAQ,CAAC,CAAC,GAAG;AACjD,oBAAM,eAAe;YACvB,OAAO;AAIL,oCACI,uBAAkB,YAAlB,mBAA2B,WAC3B,uBAAkB,YAAlB,mBAA2B;YACjC;UACF;QACF;AAGA,iBAAS,iBAAiB,WAAW,aAAa;AAClD,eAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;MACpE;IACF,GAAG,CAAC,UAAU,2BAA2B,CAAC;AAE1C,eACE;MAAkB;MAAjB;QACC,KAAK;QACL,MAAK;QACL,cAAY,MAAM,QAAQ,YAAY,WAAW;QAEjD,UAAU;QAGV,OAAO,EAAE,eAAe,YAAY,SAAY,OAAO;QAEtD,UAAA;UAAA,iBACC;YAAC;YAAA;cACC,KAAK;cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;kBACrD,kBAAkB;gBACpB,CAAC;AACD,2BAAW,kBAAkB;cAC/B;YAAA;UACF;cAMF,wBAAC,WAAW,MAAX,EAAgB,OAAO,cACtB,cAAA,wBAAC,UAAU,IAAV,EAAa,UAAU,IAAK,GAAG,eAAe,KAAK,aAAA,CAAc,EAAA,CACpE;UACC,iBACC;YAAC;YAAA;cACC,KAAK;cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;kBACrD,kBAAkB;gBACpB,CAAC;AACD,2BAAW,kBAAkB;cAC/B;YAAA;UACF;QAAA;MAAA;IAEJ;EAEJ;AACF;AAEA,cAAc,cAAc;AAI5B,IAAM,mBAAmB;AAQzB,IAAM,aAAmB;EACvB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,4BAA4B,GAAG,WAAW,IAAI;AACpE,UAAM,UAAU,wBAAwB,kBAAkB,YAAY;AAEtE,eACE;MAAC;MAAA;QACC,eAAW;QACX,UAAU;QACT,GAAG;QACJ,KAAK;QAEL,OAAO,EAAE,UAAU,QAAQ;QAC3B,SAAS,CAAC,UAAU;;AAClB,gBAAM,qBAAqB,MAAM;AACjC,gBAAM,6BAA6B,GAAC,aAAQ,aAAR,mBAAkB,SAAS;AAC/D,cAAI,2BAA4B,4BAA2B;QAC7D;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,aAAa;AACnB,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AAcxB,IAAM,QAAc;EAClB,CAAC,OAAgC,iBAAiB;AAChD,UAAM,EAAE,YAAY,MAAM,UAAU,aAAa,cAAc,GAAG,WAAW,IAAI;AACjF,UAAM,CAAC,OAAO,MAAM,OAAO,IAAI,qBAAqB;MAClD,MAAM;MACN,aAAa;MACb,UAAU;IACZ,CAAC;AACD,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,MAC/B,cAAA;MAAC;MAAA;QACC;QACC,GAAG;QACJ,KAAK;QACL,SAAS,MAAM,QAAQ,KAAK;QAC5B,SAAS,eAAe,MAAM,OAAO;QACrC,UAAU,eAAe,MAAM,QAAQ;QACvC,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU;AAChE,gBAAM,cAAc,aAAa,cAAc,OAAO;QACxD,CAAC;QACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,gBAAM,EAAE,GAAG,EAAE,IAAI,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,MAAM;AACrD,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;AAC5E,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;QAC9E,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,cAAc,aAAa,cAAc,QAAQ;AACvD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;AACpE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;QACtE,CAAC;QACD,YAAY,qBAAqB,MAAM,YAAY,CAAC,UAAU;AAC5D,gBAAM,EAAE,GAAG,EAAE,IAAI,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,KAAK;AACpD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,kBAAQ,KAAK;QACf,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,MAAM,cAAc;AASpB,IAAM,CAAC,0BAA0B,0BAA0B,IAAI,mBAAmB,YAAY;EAC5F,UAAU;EAAC;AACb,CAAC;AAsBD,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM;MACJ;MACA,OAAO;MACP,UAAU;MACV;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,UAAM,CAAC,MAAM,OAAO,IAAU,eAAkC,IAAI;AACpE,UAAM,eAAe,gBAAgB,cAAc,CAACA,UAAS,QAAQA,KAAI,CAAC;AAC1E,UAAM,kBAAwB,aAAwC,IAAI;AAC1E,UAAM,gBAAsB,aAAwC,IAAI;AACxE,UAAM,WAAW,gBAAgB,QAAQ;AACzC,UAAM,yBAA+B,aAAO,CAAC;AAC7C,UAAM,6BAAmC,aAAO,QAAQ;AACxD,UAAM,gBAAsB,aAAO,CAAC;AACpC,UAAM,EAAE,YAAY,cAAc,IAAI;AACtC,UAAM,cAAc,eAAe,MAAM;;AAGvC,YAAM,iBAAiB,6BAAM,SAAS,SAAS;AAC/C,UAAI,eAAgB,eAAQ,aAAR,mBAAkB;AACtC,cAAQ;IACV,CAAC;AAED,UAAM,aAAmB;MACvB,CAACC,cAAqB;AACpB,YAAI,CAACA,aAAYA,cAAa,SAAU;AACxC,eAAO,aAAa,cAAc,OAAO;AACzC,+BAAuB,WAAU,oBAAI,KAAK,GAAE,QAAQ;AACpD,sBAAc,UAAU,OAAO,WAAW,aAAaA,SAAQ;MACjE;MACA,CAAC,WAAW;IACd;AAEM,IAAA,gBAAU,MAAM;AACpB,YAAM,WAAW,QAAQ;AACzB,UAAI,UAAU;AACZ,cAAM,eAAe,MAAM;AACzB,qBAAW,2BAA2B,OAAO;AAC7C;QACF;AACA,cAAM,cAAc,MAAM;AACxB,gBAAM,eAAc,oBAAI,KAAK,GAAE,QAAQ,IAAI,uBAAuB;AAClE,qCAA2B,UAAU,2BAA2B,UAAU;AAC1E,iBAAO,aAAa,cAAc,OAAO;AACzC;QACF;AACA,iBAAS,iBAAiB,gBAAgB,WAAW;AACrD,iBAAS,iBAAiB,iBAAiB,YAAY;AACvD,eAAO,MAAM;AACX,mBAAS,oBAAoB,gBAAgB,WAAW;AACxD,mBAAS,oBAAoB,iBAAiB,YAAY;QAC5D;MACF;IACF,GAAG,CAAC,QAAQ,UAAU,UAAU,SAAS,UAAU,UAAU,CAAC;AAKxD,IAAA,gBAAU,MAAM;AACpB,UAAI,QAAQ,CAAC,QAAQ,iBAAiB,QAAS,YAAW,QAAQ;IACpE,GAAG,CAAC,MAAM,UAAU,QAAQ,kBAAkB,UAAU,CAAC;AAEnD,IAAA,gBAAU,MAAM;AACpB,iBAAW;AACX,aAAO,MAAM,cAAc;IAC7B,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,UAAM,sBAA4B,cAAQ,MAAM;AAC9C,aAAO,OAAO,uBAAuB,IAAI,IAAI;IAC/C,GAAG,CAAC,IAAI,CAAC;AAET,QAAI,CAAC,QAAQ,SAAU,QAAO;AAE9B,eACE,yBAAA,6BAAA,EACG,UAAA;MAAA,2BACC;QAAC;QAAA;UACC;UAEA,MAAK;UACL,aAAW,SAAS,eAAe,cAAc;UACjD,eAAW;UAEV,UAAA;QAAA;MACH;UAGF,wBAAC,0BAAA,EAAyB,OAAO,cAAc,SAAS,aACrD,UAAS;YACR,wBAAC,WAAW,UAAX,EAAoB,OAAO,cAC1B,cAAA;UAAkB;UAAjB;YACC,SAAO;YACP,iBAAiB,qBAAqB,iBAAiB,MAAM;AAC3D,kBAAI,CAAC,QAAQ,+BAA+B,QAAS,aAAY;AACjE,sBAAQ,+BAA+B,UAAU;YACnD,CAAC;YAED,cAAA;cAAC,UAAU;cAAV;gBAEC,MAAK;gBACL,aAAU;gBACV,eAAW;gBACX,UAAU;gBACV,cAAY,OAAO,SAAS;gBAC5B,wBAAsB,QAAQ;gBAC7B,GAAG;gBACJ,KAAK;gBACL,OAAO,EAAE,YAAY,QAAQ,aAAa,QAAQ,GAAG,MAAM,MAAM;gBACjE,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,sBAAI,MAAM,QAAQ,SAAU;AAC5B,qEAAkB,MAAM;AACxB,sBAAI,CAAC,MAAM,YAAY,kBAAkB;AACvC,4BAAQ,+BAA+B,UAAU;AACjD,gCAAY;kBACd;gBACF,CAAC;gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,MAAM,WAAW,EAAG;AACxB,kCAAgB,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;gBACjE,CAAC;gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,CAAC,gBAAgB,QAAS;AAC9B,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,sBAAsB,QAAQ,cAAc,OAAO;AACzD,wBAAM,oBAAoB,CAAC,QAAQ,OAAO,EAAE,SAAS,QAAQ,cAAc;AAC3E,wBAAM,QAAQ,CAAC,QAAQ,IAAI,EAAE,SAAS,QAAQ,cAAc,IACxD,KAAK,MACL,KAAK;AACT,wBAAM,WAAW,oBAAoB,MAAM,GAAG,CAAC,IAAI;AACnD,wBAAM,WAAW,CAAC,oBAAoB,MAAM,GAAG,CAAC,IAAI;AACpD,wBAAM,kBAAkB,MAAM,gBAAgB,UAAU,KAAK;AAC7D,wBAAM,QAAQ,EAAE,GAAG,UAAU,GAAG,SAAS;AACzC,wBAAM,cAAc,EAAE,eAAe,OAAO,MAAM;AAClD,sBAAI,qBAAqB;AACvB,kCAAc,UAAU;AACxB,iDAA6B,kBAAkB,aAAa,aAAa;sBACvE,UAAU;oBACZ,CAAC;kBACH,WAAW,mBAAmB,OAAO,QAAQ,gBAAgB,eAAe,GAAG;AAC7E,kCAAc,UAAU;AACxB,iDAA6B,mBAAmB,cAAc,aAAa;sBACzE,UAAU;oBACZ,CAAC;AACA,0BAAM,OAAuB,kBAAkB,MAAM,SAAS;kBACjE,WAAW,KAAK,IAAI,CAAC,IAAI,mBAAmB,KAAK,IAAI,CAAC,IAAI,iBAAiB;AAGzE,oCAAgB,UAAU;kBAC5B;gBACF,CAAC;gBACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,wBAAM,QAAQ,cAAc;AAC5B,wBAAM,SAAS,MAAM;AACrB,sBAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,2BAAO,sBAAsB,MAAM,SAAS;kBAC9C;AACA,gCAAc,UAAU;AACxB,kCAAgB,UAAU;AAC1B,sBAAI,OAAO;AACT,0BAAM,QAAQ,MAAM;AACpB,0BAAM,cAAc,EAAE,eAAe,OAAO,MAAM;AAClD,wBACE,mBAAmB,OAAO,QAAQ,gBAAgB,QAAQ,cAAc,GACxE;AACA,mDAA6B,iBAAiB,YAAY,aAAa;wBACrE,UAAU;sBACZ,CAAC;oBACH,OAAO;AACL;wBACE;wBACA;wBACA;wBACA;0BACE,UAAU;wBACZ;sBACF;oBACF;AAGA,0BAAM,iBAAiB,SAAS,CAACC,WAAUA,OAAM,eAAe,GAAG;sBACjE,MAAM;oBACR,CAAC;kBACH;gBACF,CAAC;cAAA;YACH;UAAA;QACF,EAAA,CACF;QACA,QAAQ;MACV,EAAA,CACF;IAAA,EAAA,CACF;EAEJ;AACF;AAQA,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,cAAc,UAAU,GAAG,cAAc,IAAI;AACrD,QAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,QAAM,CAAC,oBAAoB,qBAAqB,IAAU,eAAS,KAAK;AACxE,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,KAAK;AAG1D,eAAa,MAAM,sBAAsB,IAAI,CAAC;AAGxC,EAAA,gBAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,WAAW,MAAM,eAAe,IAAI,GAAG,GAAI;AAChE,WAAO,MAAM,OAAO,aAAa,KAAK;EACxC,GAAG,CAAC,CAAC;AAEL,SAAO,cAAc,WACnB,wBAAC,QAAA,EAAO,SAAO,MACb,cAAA,wBAAC,gBAAA,EAAgB,GAAG,eACjB,UAAA,0BACC,yBAAA,6BAAA,EACG,UAAA;IAAA,QAAQ;IAAM;IAAE;EAAA,EAAA,CACnB,EAAA,CAEJ,EAAA,CACF;AAEJ;AAMA,IAAM,aAAa;AAMnB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,cAAc,GAAG,WAAW,IAAI;AACxC,eAAO,wBAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,aAAA,CAAc;EAC3D;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,mBAAmB;AAKzB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,cAAc,GAAG,iBAAiB,IAAI;AAC9C,eAAO,wBAAC,UAAU,KAAV,EAAe,GAAG,kBAAkB,KAAK,aAAA,CAAc;EACjE;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,cAAc;AAapB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,SAAS,GAAG,YAAY,IAAI;AAEpC,QAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,cAAQ;QACN,0CAA0C,WAAW;MACvD;AACA,aAAO;IACT;AAEA,eACE,wBAAC,sBAAA,EAAqB,SAAkB,SAAO,MAC7C,cAAA,wBAAC,YAAA,EAAY,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAClD;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,cAAc,GAAG,WAAW,IAAI;AACxC,UAAM,qBAAqB,2BAA2B,YAAY,YAAY;AAE9E,eACE,wBAAC,sBAAA,EAAqB,SAAO,MAC3B,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACJ,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,OAAO;MAAA;IACzE,EAAA,CACF;EAEJ;AACF;AAEA,WAAW,cAAc;AASzB,IAAM,uBAA6B,iBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,cAAc,SAAS,GAAG,qBAAqB,IAAI;AAE3D,aACE;IAAC,UAAU;IAAV;MACC,qCAAkC;MAClC,iCAA+B,WAAW;MACzC,GAAG;MACJ,KAAK;IAAA;EACP;AAEJ,CAAC;AAED,SAAS,uBAAuB,WAAwB;AACtD,QAAM,cAAwB,CAAC;AAC/B,QAAM,aAAa,MAAM,KAAK,UAAU,UAAU;AAElD,aAAW,QAAQ,CAAC,SAAS;AAC3B,QAAI,KAAK,aAAa,KAAK,aAAa,KAAK,YAAa,aAAY,KAAK,KAAK,WAAW;AAC3F,QAAI,cAAc,IAAI,GAAG;AACvB,YAAM,WAAW,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,YAAY;AAC1E,YAAM,aAAa,KAAK,QAAQ,8BAA8B;AAE9D,UAAI,CAAC,UAAU;AACb,YAAI,YAAY;AACd,gBAAM,UAAU,KAAK,QAAQ;AAC7B,cAAI,QAAS,aAAY,KAAK,OAAO;QACvC,OAAO;AACL,sBAAY,KAAK,GAAG,uBAAuB,IAAI,CAAC;QAClD;MACF;IACF;EACF,CAAC;AAID,SAAO;AACT;AAIA,SAAS,6BAIP,MACA,SACA,QACA,EAAE,SAAS,GACX;AACA,QAAM,gBAAgB,OAAO,cAAc;AAC3C,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,MAAM,YAAY,MAAM,OAAO,CAAC;AAC/E,MAAI,QAAS,eAAc,iBAAiB,MAAM,SAA0B,EAAE,MAAM,KAAK,CAAC;AAE1F,MAAI,UAAU;AACZ,gCAA4B,eAAe,KAAK;EAClD,OAAO;AACL,kBAAc,cAAc,KAAK;EACnC;AACF;AAEA,IAAM,qBAAqB,CACzB,OACA,WACA,YAAY,MACT;AACH,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,WAAW,SAAS;AAC1B,MAAI,cAAc,UAAU,cAAc,SAAS;AACjD,WAAO,YAAY,SAAS;EAC9B,OAAO;AACL,WAAO,CAAC,YAAY,SAAS;EAC/B;AACF;AAEA,SAAS,aAAa,WAAW,MAAM;AAAC,GAAG;AACzC,QAAM,KAAK,eAAe,QAAQ;AAClC,mBAAgB,MAAM;AACpB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,WAAO,OAAO,sBAAsB,MAAO,OAAO,OAAO,sBAAsB,EAAE,CAAE;AACnF,WAAO,MAAM;AACX,aAAO,qBAAqB,IAAI;AAChC,aAAO,qBAAqB,IAAI;IAClC;EACF,GAAG,CAAC,EAAE,CAAC;AACT;AAEA,SAAS,cAAc,MAAgC;AACrD,SAAO,KAAK,aAAa,KAAK;AAChC;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAEA,SAAS,WAAW,YAA2B;AAC7C,QAAM,2BAA2B,SAAS;AAC1C,SAAO,WAAW,KAAK,CAAC,cAAc;AAEpC,QAAI,cAAc,yBAA0B,QAAO;AACnD,cAAU,MAAM;AAChB,WAAO,SAAS,kBAAkB;EACpC,CAAC;AACH;AAEA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAMC,QAAO;AACb,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,SAAS;AACf,IAAM,QAAQ;", "names": ["node", "duration", "event", "Root"]}