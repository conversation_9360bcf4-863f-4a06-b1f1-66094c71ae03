# Installation
> `npm install --save @types/better-sqlite3`

# Summary
This package contains type definitions for better-sqlite3 (https://github.com/JoshuaWise/better-sqlite3).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/better-sqlite3.

### Additional Details
 * Last updated: Fri, 04 Apr 2025 18:38:14 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>](https://github.com/Mo<PERSON>ent), [<PERSON><PERSON>](https://github.com/matrumz), [<PERSON>guila<PERSON>](https://github.com/sant123), [<PERSON>](https://github.com/loghorn), [<PERSON>](https://github.com/andykais), [<PERSON>](https://github.com/mrkstwrt), [<PERSON><PERSON><PERSON>](https://github.com/stamerf), and [<PERSON><PERSON>](https://github.com/beenotung).
