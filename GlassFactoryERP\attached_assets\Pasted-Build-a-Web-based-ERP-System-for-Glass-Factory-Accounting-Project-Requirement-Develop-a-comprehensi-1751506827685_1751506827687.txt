Build a Web-based ERP System for Glass Factory Accounting
Project Requirement:
Develop a comprehensive ERP system tailored for a glass manufacturing factory’s accounting and operations management. The system must include company settings with logo display on the interface and print pages, and consist of the following modules:

System Components:
Company Settings:

Register company details (name, address, contact info).

Upload company logo.

Display company data and logo on all user interfaces and printouts.

Core Modules:

1. Sales Module
Add customers with full details (name, phone, address, optional tax number).

Add services (service name, price).

Create sales invoices with automatic calculation of square meters (length × width × quantity ÷ 1,000,000), row totals, discounts, taxes, paid and balance amounts.

Invoice statuses: Draft (no accounting entries), Final (automatic accounting entries), Cancelled (accounting reversal entries).

Manage invoices with options to edit, print, pay, and cancel.

Automatic accounting entries for sales and payments.

Dedicated settings page for accounting entries configuration.

2. Purchases Module
Register suppliers and create purchase invoices automatically only (manual invoice creation not allowed).

Manage supplier payments and account statements.

Support printing and saving invoices as PDF.

Link purchases to stock receipt and inventory.

3. Payroll Module
Register employees (auto-generated employee code, name, position, branch, basic salary, working hours).

Manage payroll operations including salaries due, advances, bonuses, deductions, overtime, and payments.

Automatic accounting entries for each operation, creating accounts if they don’t exist.

Permanent data saving with editing and deletion capabilities.

Payroll accounting configuration page for linking accounts to operations.

4. Inventory Module (with Moving Average Method)
Main interface with buttons: Manage Warehouses, Manage Items, Stock Movements, Movement Reports, Available Items.

Warehouse management: auto-generated warehouse code, name, location, description; add, list, edit, delete warehouses.

Item management: auto-generated item code, item name, warehouse, unit, box content, support for “has dimensions” with length, width, area auto-calculated.

Stock movements: Receive and issue items with details (date, order number, invoice number, warehouse, supplier/client, item, quantity, price, notes), automatic quantity calculations for items with dimensions, auto box count, listing with edit/delete.

Detailed movement reports with filters (glass type, warehouse, date, client/supplier, item type).

Inventory accounting settings with automatic journal entries for receipts, returns, issues, and return issues.

Integration with Purchases Module: automatic purchase invoice creation upon receipt; status update on returns.

5. Manufacturing Module
Manage manufacturing processes related to glass production.

Link components, record production activities, and cost tracking.

6. Accounting Module
Manage chart of accounts, journal entries, financial reports (general ledger, trial balance, income statement, balance sheet).

Integrate accounting entries from all other modules with automatic posting.

Accounting entry settings page for easy account mapping and modification without code changes.

General Requirements:
Automatic and persistent data saving in the database to avoid data loss on program closure or system restart.

No disruption or regression of existing functionalities when adding new modules.

Full integration and smooth workflow between all modules.

Comprehensive testing after each development phase before moving on.

Complete documentation of all updates and changes.

Special Notes for Each Module:
Strictly avoid causing any errors or disruptions to existing system features.

All accounting entries must post automatically according to defined rules.

Support data editing, deletion, and reversal operations.

Provide user-friendly and clear interfaces.

Enable printing and saving of reports and invoices in PDF format.

Please confirm receipt of this prompt and start development immediately according to priorities, ensuring all the above details are carefully implemented.

