import { useQuery } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";
import LanguageSwitcher from "@/components/ui/language-switcher";
import {
  Building,
  Home,
  Settings,
  ShoppingCart,
  Truck,
  Package,
  Cog,
  Users,
  Calculator,
  BarChart3,
  Factory
} from "lucide-react";

interface NavItem {
  nameKey: string;
  path: string;
  icon: React.ComponentType<any>;
}

interface NavSection {
  titleKey: string;
  items: NavItem[];
}

const navigationSections: NavSection[] = [
  {
    titleKey: "nav.main",
    items: [
      { nameKey: "nav.dashboard", path: "/dashboard", icon: Home },
      { nameKey: "nav.companySettings", path: "/company-settings", icon: Building },
    ],
  },
  {
    titleKey: "nav.operations",
    items: [
      { nameKey: "nav.sales", path: "/sales", icon: ShoppingCart },
      { nameKey: "nav.purchases", path: "/purchases", icon: Truck },
      { nameKey: "nav.inventory", path: "/inventory", icon: Package },
      { nameKey: "nav.manufacturing", path: "/manufacturing", icon: Factory },
    ],
  },
  {
    titleKey: "nav.humanResources",
    items: [
      { nameKey: "nav.payroll", path: "/payroll", icon: Users },
    ],
  },
  {
    titleKey: "nav.finance",
    items: [
      { nameKey: "nav.accounting", path: "/accounting", icon: Calculator },
      { nameKey: "nav.reports", path: "/reports", icon: BarChart3 },
    ],
  },
];

export default function Sidebar() {
  const [location] = useLocation();
  const { t } = useLanguage();
  
  const { data: company } = useQuery({
    queryKey: ["/api/company"],
  });

  const isActive = (path: string) => {
    if (path === "/dashboard" && (location === "/" || location === "/dashboard")) {
      return true;
    }
    return location === path || location.startsWith(path + "/");
  };

  return (
    <div className="bg-slate-800 text-white w-64 fixed h-full overflow-y-auto">
      {/* Company Header */}
      <div className="p-6 border-b border-slate-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              {(company as any)?.logoUrl ? (
                <img
                  src={(company as any).logoUrl}
                  alt="Company Logo"
                  className="w-full h-full object-contain rounded-lg"
                />
              ) : (
                <Factory className="text-white text-lg" />
              )}
            </div>
            <div>
              <h1 className="font-semibold text-lg">
                {(company as any)?.name || "GlassTech Industries"}
              </h1>
              <p className="text-slate-400 text-sm">ERP System</p>
            </div>
          </div>
          <LanguageSwitcher />
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="p-4 space-y-2">
        {navigationSections.map((section) => (
          <div key={section.titleKey}>
            <h3 className="text-slate-400 text-xs uppercase tracking-wider font-medium mb-3">
              {t(section.titleKey)}
            </h3>
            <div className="space-y-1">
              {section.items.map((item) => {
                const Icon = item.icon;
                const active = isActive(item.path);
                
                return (
                  <Link key={item.path} href={item.path}>
                    <a
                      className={cn(
                        "flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors",
                        active
                          ? "bg-primary text-white"
                          : "hover:bg-slate-700 text-slate-300 hover:text-white"
                      )}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{t(item.nameKey)}</span>
                    </a>
                  </Link>
                );
              })}
            </div>
            {section !== navigationSections[navigationSections.length - 1] && (
              <div className="mt-6" />
            )}
          </div>
        ))}
      </nav>
    </div>
  );
}
