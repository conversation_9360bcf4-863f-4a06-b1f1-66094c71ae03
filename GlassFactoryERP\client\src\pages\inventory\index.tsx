import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCurrency, getStatusColor } from "@/lib/utils";
import { 
  Warehouse, 
  Package, 
  ArrowLeftRight, 
  BarChart3, 
  List,
  Plus,
  Search,
  Filter
} from "lucide-react";

export default function InventoryIndex() {
  const [searchTerm, setSearchTerm] = useState("");
  const [warehouseFilter, setWarehouseFilter] = useState("all");

  const { data: inventoryItems, isLoading } = useQuery({
    queryKey: ["/api/inventory/items"],
  });

  const { data: warehouses } = useQuery({
    queryKey: ["/api/warehouses"],
  });

  const { data: movements } = useQuery({
    queryKey: ["/api/inventory/movements"],
  });

  const filteredItems = inventoryItems?.filter((item: any) => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.code.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesWarehouse = warehouseFilter === "all" || item.warehouseId.toString() === warehouseFilter;
    return matchesSearch && matchesWarehouse;
  });

  const getStockStatus = (currentQuantity: number, minStockLevel: number) => {
    if (currentQuantity <= 0) return "Out of Stock";
    if (currentQuantity <= minStockLevel) return "Low Stock";
    return "In Stock";
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Inventory Management</h2>
        <p className="text-slate-600">Manage warehouses, items, and stock movements with moving average costing</p>
      </div>

      {/* Inventory Action Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Warehouse className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <p className="font-medium text-slate-800">Manage Warehouses</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Package className="h-8 w-8 text-emerald-600 mx-auto mb-3" />
            <p className="font-medium text-slate-800">Manage Items</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <ArrowLeftRight className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <p className="font-medium text-slate-800">Stock Movements</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <BarChart3 className="h-8 w-8 text-amber-600 mx-auto mb-3" />
            <p className="font-medium text-slate-800">Movement Reports</p>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <List className="h-8 w-8 text-red-600 mx-auto mb-3" />
            <p className="font-medium text-slate-800">Available Items</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by item name or code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={warehouseFilter} onValueChange={setWarehouseFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by warehouse" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Warehouses</SelectItem>
                {warehouses?.map((warehouse: any) => (
                  <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                    {warehouse.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Stock Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Current Stock Overview</CardTitle>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
              <Button size="sm" variant="outline">
                Stock Receipt
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Item Code</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Item Name</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Warehouse</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Quantity</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Unit</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Avg. Cost</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Total Value</th>
                  <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-200">
                {filteredItems?.length ? (
                  filteredItems.map((item: any) => {
                    const status = getStockStatus(Number(item.currentQuantity), Number(item.minStockLevel));
                    return (
                      <tr key={item.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {item.code}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {item.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {item.warehouse?.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-800">
                          {Number(item.currentQuantity).toFixed(3)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {item.unit}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-800">
                          {formatCurrency(item.averageCost)}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {formatCurrency(item.totalValue)}
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={getStatusColor(status)}>
                            {status}
                          </Badge>
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={8} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center space-y-3">
                        <Package className="h-12 w-12 text-gray-400" />
                        <div>
                          <p className="text-gray-500 font-medium">No inventory items found</p>
                          <p className="text-gray-400 text-sm">Add items to your warehouse to get started</p>
                        </div>
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Item
                        </Button>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
