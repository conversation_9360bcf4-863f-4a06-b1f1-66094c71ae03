{"version": 3, "sources": ["../../../src/singlestore-core/columns/year.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreYearBuilderInitial<TName extends string> = SingleStoreYearBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreYear';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreYearBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreYear'>>\n\textends SingleStoreColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreYearBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SingleStoreYear');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreYear<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreYear<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreYear<\n\tT extends ColumnBaseConfig<'number', 'SingleStoreYear'>,\n> extends SingleStoreColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreYear';\n\n\tgetSQLType(): string {\n\t\treturn `year`;\n\t}\n}\n\nexport function year(): SingleStoreYearBuilderInitial<''>;\nexport function year<TName extends string>(name: TName): SingleStoreYearBuilderInitial<TName>;\nexport function year(name?: string) {\n\treturn new SingleStoreYearBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA4D;AAYrD,MAAM,+BACJ,uCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,iBAAiB;AAAA,EACxC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAEH,gCAAqB;AAAA,EAC9B,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,uBAAuB,QAAQ,EAAE;AAC7C;", "names": []}