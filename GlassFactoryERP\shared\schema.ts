import { pgTable, text, serial, integer, boolean, timestamp, decimal, varchar, json } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Company Settings
export const companies = pgTable("companies", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address"),
  phone: text("phone"),
  email: text("email"),
  taxNumber: text("tax_number"),
  logoUrl: text("logo_url"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Customers
export const customers = pgTable("customers", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Suppliers
export const suppliers = pgTable("suppliers", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Services
export const services = pgTable("services", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  unit: text("unit").default("sq.m"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Sales Invoices
export const salesInvoices = pgTable("sales_invoices", {
  id: serial("id").primaryKey(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  customerId: integer("customer_id").references(() => customers.id).notNull(),
  invoiceDate: timestamp("invoice_date").notNull(),
  subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
  discountPercent: decimal("discount_percent", { precision: 5, scale: 2 }).default("0"),
  discountAmount: decimal("discount_amount", { precision: 10, scale: 2 }).default("0"),
  taxRate: decimal("tax_rate", { precision: 5, scale: 2 }).default("0"),
  taxAmount: decimal("tax_amount", { precision: 10, scale: 2 }).default("0"),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 10, scale: 2 }).default("0"),
  balance: decimal("balance", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull().default("draft"), // draft, final, cancelled
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Sales Invoice Items
export const salesInvoiceItems = pgTable("sales_invoice_items", {
  id: serial("id").primaryKey(),
  invoiceId: integer("invoice_id").references(() => salesInvoices.id).notNull(),
  serviceId: integer("service_id").references(() => services.id).notNull(),
  length: decimal("length", { precision: 8, scale: 2 }),
  width: decimal("width", { precision: 8, scale: 2 }),
  quantity: integer("quantity").notNull(),
  squareMeters: decimal("square_meters", { precision: 10, scale: 6 }),
  unitPrice: decimal("unit_price", { precision: 10, scale: 2 }).notNull(),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
});

// Employees
export const employees = pgTable("employees", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  position: text("position"),
  branch: text("branch"),
  basicSalary: decimal("basic_salary", { precision: 10, scale: 2 }),
  workingHours: integer("working_hours").default(8),
  phone: text("phone"),
  address: text("address"),
  hireDate: timestamp("hire_date"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Payroll Records
export const payrollRecords = pgTable("payroll_records", {
  id: serial("id").primaryKey(),
  employeeId: integer("employee_id").references(() => employees.id).notNull(),
  payrollDate: timestamp("payroll_date").notNull(),
  baseSalary: decimal("base_salary", { precision: 10, scale: 2 }).notNull(),
  overtimeHours: integer("overtime_hours").default(0),
  overtimeRate: decimal("overtime_rate", { precision: 10, scale: 2 }).default("0"),
  overtimeAmount: decimal("overtime_amount", { precision: 10, scale: 2 }).default("0"),
  bonuses: decimal("bonuses", { precision: 10, scale: 2 }).default("0"),
  deductions: decimal("deductions", { precision: 10, scale: 2 }).default("0"),
  advances: decimal("advances", { precision: 10, scale: 2 }).default("0"),
  grossPay: decimal("gross_pay", { precision: 10, scale: 2 }).notNull(),
  netPay: decimal("net_pay", { precision: 10, scale: 2 }).notNull(),
  status: text("status").default("pending"), // pending, paid
  createdAt: timestamp("created_at").defaultNow(),
});

// Warehouses
export const warehouses = pgTable("warehouses", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  location: text("location"),
  description: text("description"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Inventory Items
export const inventoryItems = pgTable("inventory_items", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  warehouseId: integer("warehouse_id").references(() => warehouses.id).notNull(),
  unit: text("unit").notNull(),
  boxContent: integer("box_content").default(1),
  hasDimensions: boolean("has_dimensions").default(false),
  currentQuantity: decimal("current_quantity", { precision: 10, scale: 3 }).default("0"),
  averageCost: decimal("average_cost", { precision: 10, scale: 4 }).default("0"),
  totalValue: decimal("total_value", { precision: 12, scale: 2 }).default("0"),
  minStockLevel: decimal("min_stock_level", { precision: 10, scale: 3 }).default("0"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Stock Movements
export const stockMovements = pgTable("stock_movements", {
  id: serial("id").primaryKey(),
  movementDate: timestamp("movement_date").notNull(),
  movementType: text("movement_type").notNull(), // receive, issue, return_receive, return_issue
  orderNumber: text("order_number"),
  invoiceNumber: text("invoice_number"),
  warehouseId: integer("warehouse_id").references(() => warehouses.id).notNull(),
  itemId: integer("item_id").references(() => inventoryItems.id).notNull(),
  supplierId: integer("supplier_id").references(() => suppliers.id),
  customerId: integer("customer_id").references(() => customers.id),
  quantity: decimal("quantity", { precision: 10, scale: 3 }).notNull(),
  unitCost: decimal("unit_cost", { precision: 10, scale: 4 }),
  totalCost: decimal("total_cost", { precision: 12, scale: 2 }),
  length: decimal("length", { precision: 8, scale: 2 }),
  width: decimal("width", { precision: 8, scale: 2 }),
  area: decimal("area", { precision: 10, scale: 6 }),
  boxCount: integer("box_count"),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Purchase Invoices
export const purchaseInvoices = pgTable("purchase_invoices", {
  id: serial("id").primaryKey(),
  invoiceNumber: text("invoice_number").notNull().unique(),
  supplierId: integer("supplier_id").references(() => suppliers.id).notNull(),
  invoiceDate: timestamp("invoice_date").notNull(),
  subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
  taxAmount: decimal("tax_amount", { precision: 10, scale: 2 }).default("0"),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
  paidAmount: decimal("paid_amount", { precision: 10, scale: 2 }).default("0"),
  balance: decimal("balance", { precision: 10, scale: 2 }).notNull(),
  status: text("status").default("pending"), // pending, paid
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Chart of Accounts
export const chartOfAccounts = pgTable("chart_of_accounts", {
  id: serial("id").primaryKey(),
  accountCode: text("account_code").notNull().unique(),
  accountName: text("account_name").notNull(),
  accountType: text("account_type").notNull(), // asset, liability, equity, revenue, expense
  parentId: integer("parent_id").references(() => chartOfAccounts.id),
  level: integer("level").default(1),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Journal Entries
export const journalEntries = pgTable("journal_entries", {
  id: serial("id").primaryKey(),
  entryNumber: text("entry_number").notNull().unique(),
  entryDate: timestamp("entry_date").notNull(),
  description: text("description").notNull(),
  reference: text("reference"),
  sourceModule: text("source_module"), // sales, purchases, payroll, inventory
  sourceId: integer("source_id"),
  totalDebit: decimal("total_debit", { precision: 12, scale: 2 }).notNull(),
  totalCredit: decimal("total_credit", { precision: 12, scale: 2 }).notNull(),
  posted: boolean("posted").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

// Journal Entry Details
export const journalEntryDetails = pgTable("journal_entry_details", {
  id: serial("id").primaryKey(),
  entryId: integer("entry_id").references(() => journalEntries.id).notNull(),
  accountId: integer("account_id").references(() => chartOfAccounts.id).notNull(),
  debitAmount: decimal("debit_amount", { precision: 12, scale: 2 }).default("0"),
  creditAmount: decimal("credit_amount", { precision: 12, scale: 2 }).default("0"),
  description: text("description"),
});

// Users for authentication
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").default("user"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
});

// Relations
export const customersRelations = relations(customers, ({ many }) => ({
  salesInvoices: many(salesInvoices),
  stockMovements: many(stockMovements),
}));

export const suppliersRelations = relations(suppliers, ({ many }) => ({
  purchaseInvoices: many(purchaseInvoices),
  stockMovements: many(stockMovements),
}));

export const servicesRelations = relations(services, ({ many }) => ({
  salesInvoiceItems: many(salesInvoiceItems),
}));

export const salesInvoicesRelations = relations(salesInvoices, ({ one, many }) => ({
  customer: one(customers, {
    fields: [salesInvoices.customerId],
    references: [customers.id],
  }),
  items: many(salesInvoiceItems),
}));

export const salesInvoiceItemsRelations = relations(salesInvoiceItems, ({ one }) => ({
  invoice: one(salesInvoices, {
    fields: [salesInvoiceItems.invoiceId],
    references: [salesInvoices.id],
  }),
  service: one(services, {
    fields: [salesInvoiceItems.serviceId],
    references: [services.id],
  }),
}));

export const employeesRelations = relations(employees, ({ many }) => ({
  payrollRecords: many(payrollRecords),
}));

export const payrollRecordsRelations = relations(payrollRecords, ({ one }) => ({
  employee: one(employees, {
    fields: [payrollRecords.employeeId],
    references: [employees.id],
  }),
}));

export const warehousesRelations = relations(warehouses, ({ many }) => ({
  inventoryItems: many(inventoryItems),
  stockMovements: many(stockMovements),
}));

export const inventoryItemsRelations = relations(inventoryItems, ({ one, many }) => ({
  warehouse: one(warehouses, {
    fields: [inventoryItems.warehouseId],
    references: [warehouses.id],
  }),
  stockMovements: many(stockMovements),
}));

export const stockMovementsRelations = relations(stockMovements, ({ one }) => ({
  warehouse: one(warehouses, {
    fields: [stockMovements.warehouseId],
    references: [warehouses.id],
  }),
  item: one(inventoryItems, {
    fields: [stockMovements.itemId],
    references: [inventoryItems.id],
  }),
  supplier: one(suppliers, {
    fields: [stockMovements.supplierId],
    references: [suppliers.id],
  }),
  customer: one(customers, {
    fields: [stockMovements.customerId],
    references: [customers.id],
  }),
}));

export const purchaseInvoicesRelations = relations(purchaseInvoices, ({ one }) => ({
  supplier: one(suppliers, {
    fields: [purchaseInvoices.supplierId],
    references: [suppliers.id],
  }),
}));

export const chartOfAccountsRelations = relations(chartOfAccounts, ({ one, many }) => ({
  parent: one(chartOfAccounts, {
    fields: [chartOfAccounts.parentId],
    references: [chartOfAccounts.id],
  }),
  children: many(chartOfAccounts),
  journalEntryDetails: many(journalEntryDetails),
}));

export const journalEntriesRelations = relations(journalEntries, ({ many }) => ({
  details: many(journalEntryDetails),
}));

export const journalEntryDetailsRelations = relations(journalEntryDetails, ({ one }) => ({
  entry: one(journalEntries, {
    fields: [journalEntryDetails.entryId],
    references: [journalEntries.id],
  }),
  account: one(chartOfAccounts, {
    fields: [journalEntryDetails.accountId],
    references: [chartOfAccounts.id],
  }),
}));

// Insert schemas
export const insertCompanySchema = createInsertSchema(companies).omit({ id: true, createdAt: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true, createdAt: true, code: true });
export const insertSupplierSchema = createInsertSchema(suppliers).omit({ id: true, createdAt: true, code: true });
export const insertServiceSchema = createInsertSchema(services).omit({ id: true, createdAt: true });
export const insertSalesInvoiceSchema = createInsertSchema(salesInvoices).omit({ id: true, createdAt: true, updatedAt: true, invoiceNumber: true });
export const insertSalesInvoiceItemSchema = createInsertSchema(salesInvoiceItems).omit({ id: true });
export const insertEmployeeSchema = createInsertSchema(employees).omit({ id: true, createdAt: true, code: true });
export const insertPayrollRecordSchema = createInsertSchema(payrollRecords).omit({ id: true, createdAt: true });
export const insertWarehouseSchema = createInsertSchema(warehouses).omit({ id: true, createdAt: true, code: true });
export const insertInventoryItemSchema = createInsertSchema(inventoryItems).omit({ id: true, createdAt: true, code: true });
export const insertStockMovementSchema = createInsertSchema(stockMovements).omit({ id: true, createdAt: true });
export const insertPurchaseInvoiceSchema = createInsertSchema(purchaseInvoices).omit({ id: true, createdAt: true, invoiceNumber: true });
export const insertChartOfAccountSchema = createInsertSchema(chartOfAccounts).omit({ id: true, createdAt: true });
export const insertJournalEntrySchema = createInsertSchema(journalEntries).omit({ id: true, createdAt: true, entryNumber: true });
export const insertJournalEntryDetailSchema = createInsertSchema(journalEntryDetails).omit({ id: true });
export const insertUserSchema = createInsertSchema(users).omit({ id: true, createdAt: true });

// Types
export type Company = typeof companies.$inferSelect;
export type Customer = typeof customers.$inferSelect;
export type Supplier = typeof suppliers.$inferSelect;
export type Service = typeof services.$inferSelect;
export type SalesInvoice = typeof salesInvoices.$inferSelect;
export type SalesInvoiceItem = typeof salesInvoiceItems.$inferSelect;
export type Employee = typeof employees.$inferSelect;
export type PayrollRecord = typeof payrollRecords.$inferSelect;
export type Warehouse = typeof warehouses.$inferSelect;
export type InventoryItem = typeof inventoryItems.$inferSelect;
export type StockMovement = typeof stockMovements.$inferSelect;
export type PurchaseInvoice = typeof purchaseInvoices.$inferSelect;
export type ChartOfAccount = typeof chartOfAccounts.$inferSelect;
export type JournalEntry = typeof journalEntries.$inferSelect;
export type JournalEntryDetail = typeof journalEntryDetails.$inferSelect;
export type User = typeof users.$inferSelect;

export type InsertCompany = z.infer<typeof insertCompanySchema>;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;
export type InsertSupplier = z.infer<typeof insertSupplierSchema>;
export type InsertService = z.infer<typeof insertServiceSchema>;
export type InsertSalesInvoice = z.infer<typeof insertSalesInvoiceSchema>;
export type InsertSalesInvoiceItem = z.infer<typeof insertSalesInvoiceItemSchema>;
export type InsertEmployee = z.infer<typeof insertEmployeeSchema>;
export type InsertPayrollRecord = z.infer<typeof insertPayrollRecordSchema>;
export type InsertWarehouse = z.infer<typeof insertWarehouseSchema>;
export type InsertInventoryItem = z.infer<typeof insertInventoryItemSchema>;
export type InsertStockMovement = z.infer<typeof insertStockMovementSchema>;
export type InsertPurchaseInvoice = z.infer<typeof insertPurchaseInvoiceSchema>;
export type InsertChartOfAccount = z.infer<typeof insertChartOfAccountSchema>;
export type InsertJournalEntry = z.infer<typeof insertJournalEntrySchema>;
export type InsertJournalEntryDetail = z.infer<typeof insertJournalEntryDetailSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;
