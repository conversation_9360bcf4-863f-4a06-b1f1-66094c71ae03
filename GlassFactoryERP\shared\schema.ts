import { sqliteTable, text, integer, real, blob } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Company Settings
export const companies = sqliteTable("companies", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  address: text("address"),
  phone: text("phone"),
  email: text("email"),
  taxNumber: text("tax_number"),
  logoUrl: text("logo_url"),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Customers
export const customers = sqliteTable("customers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Suppliers
export const suppliers = sqliteTable("suppliers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Services
export const services = sqliteTable("services", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category").default("Glass"),
  price: real("price").notNull(),
  unit: text("unit").default("sq.m"),
  glassType: text("glass_type"),
  thickness: real("thickness"),
  color: text("color"),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
  updatedAt: integer("updated_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Sales Invoices
export const salesInvoices = sqliteTable("sales_invoices", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceNumber: text("invoice_number").notNull().unique(),
  customerId: integer("customer_id").references(() => customers.id).notNull(),
  invoiceDate: integer("invoice_date", { mode: 'timestamp' }).notNull(),
  subtotal: real("subtotal").notNull(),
  discountPercent: real("discount_percent").default(0),
  discountAmount: real("discount_amount").default(0),
  taxRate: real("tax_rate").default(0),
  taxAmount: real("tax_amount").default(0),
  total: real("total").notNull(),
  notes: text("notes"),
  status: text("status").default("draft"), // draft, sent, paid, cancelled
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Sales Invoice Items
export const salesInvoiceItems = sqliteTable("sales_invoice_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceId: integer("invoice_id").references(() => salesInvoices.id).notNull(),
  serviceId: integer("service_id").references(() => services.id).notNull(),
  description: text("description"),
  quantity: real("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Purchase Invoices
export const purchaseInvoices = sqliteTable("purchase_invoices", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceNumber: text("invoice_number").notNull().unique(),
  supplierId: integer("supplier_id").references(() => suppliers.id).notNull(),
  invoiceDate: integer("invoice_date", { mode: 'timestamp' }).notNull(),
  subtotal: real("subtotal").notNull(),
  discountPercent: real("discount_percent").default(0),
  discountAmount: real("discount_amount").default(0),
  taxRate: real("tax_rate").default(0),
  taxAmount: real("tax_amount").default(0),
  total: real("total").notNull(),
  notes: text("notes"),
  status: text("status").default("pending"), // pending, paid, cancelled
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Purchase Invoice Items
export const purchaseInvoiceItems = sqliteTable("purchase_invoice_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceId: integer("invoice_id").references(() => purchaseInvoices.id).notNull(),
  description: text("description").notNull(),
  quantity: real("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Employees
export const employees = sqliteTable("employees", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  position: text("position"),
  branch: text("branch"),
  basicSalary: real("basic_salary"),
  workingHours: integer("working_hours").default(8),
  phone: text("phone"),
  address: text("address"),
  hireDate: integer("hire_date", { mode: 'timestamp' }),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Payroll Records
export const payrollRecords = sqliteTable("payroll_records", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  employeeId: integer("employee_id").references(() => employees.id).notNull(),
  payPeriodStart: integer("pay_period_start", { mode: 'timestamp' }).notNull(),
  payPeriodEnd: integer("pay_period_end", { mode: 'timestamp' }).notNull(),
  basicPay: real("basic_pay").notNull(),
  overtime: real("overtime").default(0),
  bonuses: real("bonuses").default(0),
  deductions: real("deductions").default(0),
  grossPay: real("gross_pay").notNull(),
  netPay: real("net_pay").notNull(),
  status: text("status").default("pending"), // pending, paid
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Warehouses
export const warehouses = sqliteTable("warehouses", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  location: text("location"),
  description: text("description"),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Inventory Items
export const inventoryItems = sqliteTable("inventory_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category"),
  unit: text("unit").default("piece"),
  costPrice: real("cost_price"),
  sellingPrice: real("selling_price"),
  minStock: real("min_stock").default(0),
  maxStock: real("max_stock").default(0),
  currentStock: real("current_stock").default(0),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Stock Movements
export const stockMovements = sqliteTable("stock_movements", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  itemId: integer("item_id").references(() => inventoryItems.id).notNull(),
  warehouseId: integer("warehouse_id").references(() => warehouses.id).notNull(),
  movementType: text("movement_type").notNull(), // in, out, transfer, adjustment
  quantity: real("quantity").notNull(),
  unitCost: real("unit_cost"),
  totalCost: real("total_cost"),
  referenceType: text("reference_type"), // sales_invoice, purchase_invoice, adjustment
  referenceId: integer("reference_id"),
  notes: text("notes"),
  movementDate: integer("movement_date", { mode: 'timestamp' }).notNull(),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Chart of Accounts
export const chartOfAccounts = sqliteTable("chart_of_accounts", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  accountCode: text("account_code").notNull().unique(),
  accountName: text("account_name").notNull(),
  accountType: text("account_type").notNull(), // asset, liability, equity, revenue, expense
  parentId: integer("parent_id").references(() => chartOfAccounts.id),
  level: integer("level").default(1),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Journal Entries
export const journalEntries = sqliteTable("journal_entries", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  entryNumber: text("entry_number").notNull().unique(),
  entryDate: integer("entry_date", { mode: 'timestamp' }).notNull(),
  description: text("description").notNull(),
  referenceType: text("reference_type"), // sales_invoice, purchase_invoice, manual
  referenceId: integer("reference_id"),
  totalDebit: real("total_debit").notNull(),
  totalCredit: real("total_credit").notNull(),
  status: text("status").default("draft"), // draft, posted
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Journal Entry Lines
export const journalEntryLines = sqliteTable("journal_entry_lines", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  entryId: integer("entry_id").references(() => journalEntries.id).notNull(),
  accountId: integer("account_id").references(() => chartOfAccounts.id).notNull(),
  description: text("description"),
  debitAmount: real("debit_amount").default(0),
  creditAmount: real("credit_amount").default(0),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Users for authentication
export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").default("user"),
  active: integer("active", { mode: 'boolean' }).default(true),
  createdAt: integer("created_at", { mode: 'timestamp' }).$defaultFn(() => new Date()),
});

// Relations
export const customersRelations = relations(customers, ({ many }) => ({
  salesInvoices: many(salesInvoices),
  stockMovements: many(stockMovements),
}));

export const suppliersRelations = relations(suppliers, ({ many }) => ({
  purchaseInvoices: many(purchaseInvoices),
  stockMovements: many(stockMovements),
}));

export const servicesRelations = relations(services, ({ many }) => ({
  salesInvoiceItems: many(salesInvoiceItems),
}));

export const salesInvoicesRelations = relations(salesInvoices, ({ one, many }) => ({
  customer: one(customers, {
    fields: [salesInvoices.customerId],
    references: [customers.id],
  }),
  items: many(salesInvoiceItems),
}));

export const salesInvoiceItemsRelations = relations(salesInvoiceItems, ({ one }) => ({
  invoice: one(salesInvoices, {
    fields: [salesInvoiceItems.invoiceId],
    references: [salesInvoices.id],
  }),
  service: one(services, {
    fields: [salesInvoiceItems.serviceId],
    references: [services.id],
  }),
}));

export const purchaseInvoicesRelations = relations(purchaseInvoices, ({ one, many }) => ({
  supplier: one(suppliers, {
    fields: [purchaseInvoices.supplierId],
    references: [suppliers.id],
  }),
  items: many(purchaseInvoiceItems),
}));

export const purchaseInvoiceItemsRelations = relations(purchaseInvoiceItems, ({ one }) => ({
  invoice: one(purchaseInvoices, {
    fields: [purchaseInvoiceItems.invoiceId],
    references: [purchaseInvoices.id],
  }),
}));

export const employeesRelations = relations(employees, ({ many }) => ({
  payrollRecords: many(payrollRecords),
}));

export const payrollRecordsRelations = relations(payrollRecords, ({ one }) => ({
  employee: one(employees, {
    fields: [payrollRecords.employeeId],
    references: [employees.id],
  }),
}));

export const warehousesRelations = relations(warehouses, ({ many }) => ({
  stockMovements: many(stockMovements),
}));

export const inventoryItemsRelations = relations(inventoryItems, ({ many }) => ({
  stockMovements: many(stockMovements),
}));

export const stockMovementsRelations = relations(stockMovements, ({ one }) => ({
  item: one(inventoryItems, {
    fields: [stockMovements.itemId],
    references: [inventoryItems.id],
  }),
  warehouse: one(warehouses, {
    fields: [stockMovements.warehouseId],
    references: [warehouses.id],
  }),
}));

export const chartOfAccountsRelations = relations(chartOfAccounts, ({ one, many }) => ({
  parent: one(chartOfAccounts, {
    fields: [chartOfAccounts.parentId],
    references: [chartOfAccounts.id],
  }),
  children: many(chartOfAccounts),
  journalEntryLines: many(journalEntryLines),
}));

export const journalEntriesRelations = relations(journalEntries, ({ many }) => ({
  lines: many(journalEntryLines),
}));

export const journalEntryLinesRelations = relations(journalEntryLines, ({ one }) => ({
  entry: one(journalEntries, {
    fields: [journalEntryLines.entryId],
    references: [journalEntries.id],
  }),
  account: one(chartOfAccounts, {
    fields: [journalEntryLines.accountId],
    references: [chartOfAccounts.id],
  }),
}));

// Insert schemas for validation
export const insertCompanySchema = createInsertSchema(companies);
export const insertCustomerSchema = createInsertSchema(customers);
export const insertSupplierSchema = createInsertSchema(suppliers);
export const insertServiceSchema = createInsertSchema(services);
export const insertSalesInvoiceSchema = createInsertSchema(salesInvoices);
export const insertSalesInvoiceItemSchema = createInsertSchema(salesInvoiceItems);
export const insertPurchaseInvoiceSchema = createInsertSchema(purchaseInvoices);
export const insertPurchaseInvoiceItemSchema = createInsertSchema(purchaseInvoiceItems);
export const insertEmployeeSchema = createInsertSchema(employees);
export const insertPayrollRecordSchema = createInsertSchema(payrollRecords);
export const insertWarehouseSchema = createInsertSchema(warehouses);
export const insertInventoryItemSchema = createInsertSchema(inventoryItems);
export const insertStockMovementSchema = createInsertSchema(stockMovements);
export const insertChartOfAccountSchema = createInsertSchema(chartOfAccounts);
export const insertJournalEntrySchema = createInsertSchema(journalEntries);
export const insertJournalEntryLineSchema = createInsertSchema(journalEntryLines);
export const insertUserSchema = createInsertSchema(users);

// Types
export type Company = typeof companies.$inferSelect;
export type Customer = typeof customers.$inferSelect;
export type Supplier = typeof suppliers.$inferSelect;
export type Service = typeof services.$inferSelect;
export type SalesInvoice = typeof salesInvoices.$inferSelect;
export type SalesInvoiceItem = typeof salesInvoiceItems.$inferSelect;
export type PurchaseInvoice = typeof purchaseInvoices.$inferSelect;
export type PurchaseInvoiceItem = typeof purchaseInvoiceItems.$inferSelect;
export type Employee = typeof employees.$inferSelect;
export type PayrollRecord = typeof payrollRecords.$inferSelect;
export type Warehouse = typeof warehouses.$inferSelect;
export type InventoryItem = typeof inventoryItems.$inferSelect;
export type StockMovement = typeof stockMovements.$inferSelect;
export type ChartOfAccount = typeof chartOfAccounts.$inferSelect;
export type JournalEntry = typeof journalEntries.$inferSelect;
export type JournalEntryLine = typeof journalEntryLines.$inferSelect;
export type User = typeof users.$inferSelect;

// Insert types
export type InsertCompany = typeof companies.$inferInsert;
export type InsertCustomer = typeof customers.$inferInsert;
export type InsertSupplier = typeof suppliers.$inferInsert;
export type InsertService = typeof services.$inferInsert;
export type InsertSalesInvoice = typeof salesInvoices.$inferInsert;
export type InsertSalesInvoiceItem = typeof salesInvoiceItems.$inferInsert;
export type InsertPurchaseInvoice = typeof purchaseInvoices.$inferInsert;
export type InsertPurchaseInvoiceItem = typeof purchaseInvoiceItems.$inferInsert;
export type InsertEmployee = typeof employees.$inferInsert;
export type InsertPayrollRecord = typeof payrollRecords.$inferInsert;
export type InsertWarehouse = typeof warehouses.$inferInsert;
export type InsertInventoryItem = typeof inventoryItems.$inferInsert;
export type InsertStockMovement = typeof stockMovements.$inferInsert;
export type InsertChartOfAccount = typeof chartOfAccounts.$inferInsert;
export type InsertJournalEntry = typeof journalEntries.$inferInsert;
export type InsertJournalEntryLine = typeof journalEntryLines.$inferInsert;
export type InsertUser = typeof users.$inferInsert;
