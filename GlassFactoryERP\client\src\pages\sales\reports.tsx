import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  TrendingUp, 
  Download, 
  Calendar,
  DollarSign,
  FileText,
  Users,
  BarChart3,
  PieChart
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

export default function SalesReportsPage() {
  const [dateFrom, setDateFrom] = useState("");
  const [dateTo, setDateTo] = useState("");
  const [reportType, setReportType] = useState("summary");

  // Fetch sales data for reports
  const { data: salesData, isLoading } = useQuery({
    queryKey: ["sales-reports", dateFrom, dateTo],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (dateFrom) params.append("from", dateFrom);
      if (dateTo) params.append("to", dateTo);
      
      const response = await fetch(`/api/sales/reports?${params}`);
      if (!response.ok) throw new Error("Failed to fetch sales reports");
      return response.json();
    }
  });

  const handleExportReport = async (format: string) => {
    try {
      const params = new URLSearchParams();
      if (dateFrom) params.append("from", dateFrom);
      if (dateTo) params.append("to", dateTo);
      params.append("format", format);
      params.append("type", reportType);
      
      const response = await fetch(`/api/sales/reports/export?${params}`);
      if (!response.ok) throw new Error("Failed to export report");
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `sales-report-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Export failed:", error);
    }
  };

  const reportData = salesData || {
    summary: {
      totalSales: 0,
      totalInvoices: 0,
      averageOrderValue: 0,
      totalCustomers: 0
    },
    monthlyTrend: [],
    topCustomers: [],
    topServices: [],
    paymentStatus: {
      paid: 0,
      pending: 0,
      overdue: 0
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading sales reports...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Sales Reports</h2>
        <p className="text-slate-600">Analyze sales performance and generate detailed reports</p>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="dateFrom">From Date</Label>
              <Input
                id="dateFrom"
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="dateTo">To Date</Label>
              <Input
                id="dateTo"
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="reportType">Report Type</Label>
              <select
                id="reportType"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="summary">Sales Summary</option>
                <option value="detailed">Detailed Sales</option>
                <option value="customer">Customer Analysis</option>
                <option value="service">Service Performance</option>
                <option value="payment">Payment Status</option>
              </select>
            </div>
            <div className="flex items-end space-x-2">
              <Button onClick={() => handleExportReport("pdf")} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                PDF
              </Button>
              <Button onClick={() => handleExportReport("excel")} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Excel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Sales</p>
                <p className="text-2xl font-bold text-slate-800">
                  ${reportData.summary.totalSales.toLocaleString()}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Invoices</p>
                <p className="text-2xl font-bold text-slate-800">{reportData.summary.totalInvoices}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Avg. Order Value</p>
                <p className="text-2xl font-bold text-slate-800">
                  ${reportData.summary.averageOrderValue.toFixed(2)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Active Customers</p>
                <p className="text-2xl font-bold text-slate-800">{reportData.summary.totalCustomers}</p>
              </div>
              <Users className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Monthly Sales Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Monthly Sales Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.monthlyTrend.length > 0 ? (
                reportData.monthlyTrend.map((month: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">{month.month}</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(month.sales / Math.max(...reportData.monthlyTrend.map((m: any) => m.sales))) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">${month.sales.toLocaleString()}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-slate-500 text-center py-8">No data available for the selected period</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Payment Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Paid</span>
                </div>
                <span className="font-medium">${reportData.paymentStatus.paid.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">Pending</span>
                </div>
                <span className="font-medium">${reportData.paymentStatus.pending.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">Overdue</span>
                </div>
                <span className="font-medium">${reportData.paymentStatus.overdue.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle>Top Customers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.topCustomers.length > 0 ? (
                reportData.topCustomers.map((customer: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <p className="text-sm text-slate-600">{customer.invoices} invoices</p>
                    </div>
                    <span className="font-medium">${customer.total.toLocaleString()}</span>
                  </div>
                ))
              ) : (
                <p className="text-slate-500 text-center py-8">No customer data available</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Top Services */}
        <Card>
          <CardHeader>
            <CardTitle>Top Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.topServices.length > 0 ? (
                reportData.topServices.map((service: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{service.name}</p>
                      <p className="text-sm text-slate-600">{service.quantity} units sold</p>
                    </div>
                    <span className="font-medium">${service.revenue.toLocaleString()}</span>
                  </div>
                ))
              ) : (
                <p className="text-slate-500 text-center py-8">No service data available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
