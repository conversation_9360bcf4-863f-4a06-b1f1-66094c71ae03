import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Trash2, Plus, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { calculateSquareMeters, formatCurrency } from "@/lib/utils";

interface InvoiceItem {
  id: string;
  serviceId: number;
  serviceName: string;
  length: number;
  width: number;
  quantity: number;
  unitPrice: number;
  squareMeters: number;
  total: number;
}

interface SalesInvoiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId?: number;
}

export default function SalesInvoiceModal({ open, onOpenChange, invoiceId }: SalesInvoiceModalProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    customerId: "",
    invoiceDate: new Date().toISOString().split('T')[0],
    discountPercent: 0,
    taxRate: 15,
    paidAmount: 0,
  });
  
  const [items, setItems] = useState<InvoiceItem[]>([]);

  const { data: customers } = useQuery({
    queryKey: ["/api/customers"],
  });

  const { data: services } = useQuery({
    queryKey: ["/api/services"],
  });

  const createInvoiceMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/sales/invoices", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/sales/invoices"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard/recent-invoices"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
      onOpenChange(false);
      resetForm();
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create invoice",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      customerId: "",
      invoiceDate: new Date().toISOString().split('T')[0],
      discountPercent: 0,
      taxRate: 15,
      paidAmount: 0,
    });
    setItems([]);
  };

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      serviceId: 0,
      serviceName: "",
      length: 0,
      width: 0,
      quantity: 1,
      unitPrice: 0,
      squareMeters: 0,
      total: 0,
    };
    setItems([...items, newItem]);
  };

  const removeItem = (id: string) => {
    setItems(items.filter(item => item.id !== id));
  };

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Update service name when service changes
        if (field === 'serviceId') {
          const service = services?.find((s: any) => s.id === value);
          if (service) {
            updatedItem.serviceName = service.name;
            updatedItem.unitPrice = Number(service.price);
          }
        }
        
        // Recalculate square meters and total
        if (['length', 'width', 'quantity', 'unitPrice'].includes(field) || field === 'serviceId') {
          updatedItem.squareMeters = calculateSquareMeters(
            updatedItem.length,
            updatedItem.width,
            updatedItem.quantity
          );
          updatedItem.total = updatedItem.squareMeters * updatedItem.unitPrice;
        }
        
        return updatedItem;
      }
      return item;
    }));
  };

  const calculations = () => {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = subtotal * (formData.discountPercent / 100);
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * (formData.taxRate / 100);
    const total = taxableAmount + taxAmount;
    const balance = total - formData.paidAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total,
      balance,
    };
  };

  const handleSubmit = (status: 'draft' | 'final') => {
    if (!formData.customerId) {
      toast({
        title: "Error",
        description: "Please select a customer",
        variant: "destructive",
      });
      return;
    }

    if (items.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one item",
        variant: "destructive",
      });
      return;
    }

    const calc = calculations();
    
    const invoiceData = {
      customerId: Number(formData.customerId),
      invoiceDate: new Date(formData.invoiceDate),
      subtotal: calc.subtotal.toString(),
      discountPercent: formData.discountPercent.toString(),
      discountAmount: calc.discountAmount.toString(),
      taxRate: formData.taxRate.toString(),
      taxAmount: calc.taxAmount.toString(),
      total: calc.total.toString(),
      paidAmount: formData.paidAmount.toString(),
      balance: calc.balance.toString(),
      status,
    };

    const itemsData = items.map(item => ({
      serviceId: item.serviceId,
      length: item.length.toString(),
      width: item.width.toString(),
      quantity: item.quantity,
      squareMeters: item.squareMeters.toString(),
      unitPrice: item.unitPrice.toString(),
      total: item.total.toString(),
    }));

    createInvoiceMutation.mutate({
      invoice: invoiceData,
      items: itemsData,
    });
  };

  const calc = calculations();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>Create Sales Invoice</DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer and Date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Customer *</Label>
              <Select
                value={formData.customerId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, customerId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select Customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers?.map((customer: any) => (
                    <SelectItem key={customer.id} value={customer.id.toString()}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Invoice Date *</Label>
              <Input
                type="date"
                value={formData.invoiceDate}
                onChange={(e) => setFormData(prev => ({ ...prev, invoiceDate: e.target.value }))}
              />
            </div>
          </div>

          {/* Invoice Items */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-slate-800">Invoice Items</h3>
              <Button onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-slate-50">
                    <tr>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Service</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Length (m)</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Width (m)</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Qty</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Sq.M</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Price</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Total</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Action</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-4 py-3">
                          <Select
                            value={item.serviceId.toString()}
                            onValueChange={(value) => updateItem(item.id, 'serviceId', Number(value))}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Select Service" />
                            </SelectTrigger>
                            <SelectContent>
                              {services?.map((service: any) => (
                                <SelectItem key={service.id} value={service.id.toString()}>
                                  {service.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </td>
                        <td className="px-4 py-3">
                          <Input
                            type="number"
                            step="0.01"
                            value={item.length}
                            onChange={(e) => updateItem(item.id, 'length', Number(e.target.value))}
                            className="w-20"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <Input
                            type="number"
                            step="0.01"
                            value={item.width}
                            onChange={(e) => updateItem(item.id, 'width', Number(e.target.value))}
                            className="w-20"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateItem(item.id, 'quantity', Number(e.target.value))}
                            className="w-20"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <span className="text-sm font-medium">
                            {item.squareMeters.toFixed(6)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <Input
                            type="number"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateItem(item.id, 'unitPrice', Number(e.target.value))}
                            className="w-24"
                          />
                        </td>
                        <td className="px-4 py-3">
                          <span className="text-sm font-medium">
                            {formatCurrency(item.total)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.id)}
                          >
                            <Trash2 className="h-4 w-4 text-red-600" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Invoice Totals */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Discount (%)</Label>
                <Input
                  type="number"
                  step="0.01"
                  max="100"
                  value={formData.discountPercent}
                  onChange={(e) => setFormData(prev => ({ ...prev, discountPercent: Number(e.target.value) }))}
                />
              </div>
              <div className="space-y-2">
                <Label>Tax Rate (%)</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.taxRate}
                  onChange={(e) => setFormData(prev => ({ ...prev, taxRate: Number(e.target.value) }))}
                />
              </div>
              <div className="space-y-2">
                <Label>Paid Amount</Label>
                <Input
                  type="number"
                  step="0.01"
                  value={formData.paidAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, paidAmount: Number(e.target.value) }))}
                />
              </div>
            </div>

            <Card>
              <CardContent className="p-4 space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">Subtotal:</span>
                  <span className="font-medium">{formatCurrency(calc.subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">Discount:</span>
                  <span className="font-medium">{formatCurrency(calc.discountAmount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">Tax:</span>
                  <span className="font-medium">{formatCurrency(calc.taxAmount)}</span>
                </div>
                <div className="border-t border-slate-300 pt-3">
                  <div className="flex justify-between text-lg font-semibold">
                    <span className="text-slate-800">Total:</span>
                    <span className="text-slate-800">{formatCurrency(calc.total)}</span>
                  </div>
                  <div className="flex justify-between text-sm mt-2">
                    <span className="text-slate-600">Balance:</span>
                    <span className="font-medium">{formatCurrency(calc.balance)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-slate-200">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSubmit('draft')}
              disabled={createInvoiceMutation.isPending}
              className="bg-amber-50 text-amber-800 border-amber-200 hover:bg-amber-100"
            >
              Save as Draft
            </Button>
            <Button
              onClick={() => handleSubmit('final')}
              disabled={createInvoiceMutation.isPending}
            >
              {createInvoiceMutation.isPending ? "Creating..." : "Finalize Invoice"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
