import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Calculator, 
  BookOpen, 
  FileText, 
  TrendingUp,
  BarChart3,
  DollarSign,
  PieChart,
  Plus,
  Settings
} from "lucide-react";

export default function AccountingIndex() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Accounting Module</h2>
        <p className="text-slate-600">Manage chart of accounts, journal entries, and financial reports</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Assets</p>
                <p className="text-2xl font-bold text-slate-800">$485,320</p>
                <p className="text-emerald-600 text-sm font-medium">+5.2% this month</p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Revenue</p>
                <p className="text-2xl font-bold text-slate-800">$127,540</p>
                <p className="text-blue-600 text-sm font-medium">Current period</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Expenses</p>
                <p className="text-2xl font-bold text-slate-800">$89,240</p>
                <p className="text-red-600 text-sm font-medium">Operating costs</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Net Profit</p>
                <p className="text-2xl font-bold text-slate-800">$38,300</p>
                <p className="text-emerald-600 text-sm font-medium">30.1% margin</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <PieChart className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="accounts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="accounts">Chart of Accounts</TabsTrigger>
          <TabsTrigger value="journal">Journal Entries</TabsTrigger>
          <TabsTrigger value="reports">Financial Reports</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="accounts" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Chart of Accounts</span>
                </CardTitle>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Account
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-slate-800">1000 - Assets</h4>
                    <p className="text-sm text-slate-600">Current balance: $485,320.00</p>
                  </div>
                  <Badge variant="secondary">Parent Account</Badge>
                </div>
                
                <div className="ml-6 space-y-2">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h5 className="font-medium text-slate-700">1100 - Current Assets</h5>
                      <p className="text-sm text-slate-500">Balance: $285,320.00</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">Edit</Button>
                      <Button variant="ghost" size="sm">Delete</Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h5 className="font-medium text-slate-700">1200 - Fixed Assets</h5>
                      <p className="text-sm text-slate-500">Balance: $200,000.00</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">Edit</Button>
                      <Button variant="ghost" size="sm">Delete</Button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-slate-800">2000 - Liabilities</h4>
                    <p className="text-sm text-slate-600">Current balance: $125,000.00</p>
                  </div>
                  <Badge variant="secondary">Parent Account</Badge>
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-slate-800">3000 - Equity</h4>
                    <p className="text-sm text-slate-600">Current balance: $360,320.00</p>
                  </div>
                  <Badge variant="secondary">Parent Account</Badge>
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-slate-800">4000 - Revenue</h4>
                    <p className="text-sm text-slate-600">Current balance: $127,540.00</p>
                  </div>
                  <Badge variant="secondary">Parent Account</Badge>
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-slate-800">5000 - Expenses</h4>
                    <p className="text-sm text-slate-600">Current balance: $89,240.00</p>
                  </div>
                  <Badge variant="secondary">Parent Account</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="journal" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Journal Entries</span>
                </CardTitle>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  New Entry
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-slate-50">
                    <tr>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Entry #</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Date</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Description</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Reference</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Source</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Amount</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Status</th>
                      <th className="text-left px-4 py-3 text-sm font-medium text-slate-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    <tr className="hover:bg-slate-50">
                      <td className="px-4 py-3 text-sm font-medium text-slate-800">JE-2024-001</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Jan 15, 2024</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Sales Invoice - INV-2024-001</td>
                      <td className="px-4 py-3 text-sm text-slate-600">INV-2024-001</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Sales</td>
                      <td className="px-4 py-3 text-sm font-medium text-slate-800">$4,580.00</td>
                      <td className="px-4 py-3">
                        <Badge className="bg-emerald-100 text-emerald-800">Posted</Badge>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm">View</Button>
                          <Button variant="ghost" size="sm">Edit</Button>
                        </div>
                      </td>
                    </tr>
                    <tr className="hover:bg-slate-50">
                      <td className="px-4 py-3 text-sm font-medium text-slate-800">JE-2024-002</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Jan 14, 2024</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Payroll - January 2024</td>
                      <td className="px-4 py-3 text-sm text-slate-600">PAY-2024-01</td>
                      <td className="px-4 py-3 text-sm text-slate-600">Payroll</td>
                      <td className="px-4 py-3 text-sm font-medium text-slate-800">$12,350.00</td>
                      <td className="px-4 py-3">
                        <Badge className="bg-emerald-100 text-emerald-800">Posted</Badge>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm">View</Button>
                          <Button variant="ghost" size="sm">Edit</Button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-5 w-5 text-blue-600" />
                  </div>
                  <span>General Ledger</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  View detailed account transactions and balances for all accounts.
                </p>
                <Button className="w-full" variant="outline">
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <Calculator className="h-5 w-5 text-emerald-600" />
                  </div>
                  <span>Trial Balance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  Summary of all account balances to ensure debits equal credits.
                </p>
                <Button className="w-full" variant="outline">
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                  </div>
                  <span>Income Statement</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  Profit and loss report showing revenue, expenses, and net income.
                </p>
                <Button className="w-full" variant="outline">
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-amber-600" />
                  </div>
                  <span>Balance Sheet</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  Financial position showing assets, liabilities, and equity.
                </p>
                <Button className="w-full" variant="outline">
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-red-600" />
                  </div>
                  <span>Cash Flow</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  Cash flow statement showing operating, investing, and financing activities.
                </p>
                <Button className="w-full" variant="outline">
                  Generate Report
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    <PieChart className="h-5 w-5 text-gray-600" />
                  </div>
                  <span>Custom Reports</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 mb-4">
                  Create custom financial reports with flexible filtering options.
                </p>
                <Button className="w-full" variant="outline">
                  Create Report
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Accounting Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Sales Accounting</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-600 mb-4">
                      Configure automatic accounting entries for sales transactions.
                    </p>
                    <Button variant="outline" className="w-full">
                      Configure Sales Accounts
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Purchase Accounting</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-600 mb-4">
                      Configure automatic accounting entries for purchase transactions.
                    </p>
                    <Button variant="outline" className="w-full">
                      Configure Purchase Accounts
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Payroll Accounting</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-600 mb-4">
                      Configure automatic accounting entries for payroll operations.
                    </p>
                    <Button variant="outline" className="w-full">
                      Configure Payroll Accounts
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Inventory Accounting</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-600 mb-4">
                      Configure automatic accounting entries for inventory movements.
                    </p>
                    <Button variant="outline" className="w-full">
                      Configure Inventory Accounts
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
