var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// server/db.ts
import Database from "better-sqlite3";
import { drizzle } from "drizzle-orm/better-sqlite3";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  chartOfAccounts: () => chartOfAccounts,
  chartOfAccountsRelations: () => chartOfAccountsRelations,
  companies: () => companies,
  customers: () => customers,
  customersRelations: () => customersRelations,
  employees: () => employees,
  employeesRelations: () => employeesRelations,
  insertChartOfAccountSchema: () => insertChartOfAccountSchema,
  insertCompanySchema: () => insertCompanySchema,
  insertCustomerSchema: () => insertCustomerSchema,
  insertEmployeeSchema: () => insertEmployeeSchema,
  insertInventoryItemSchema: () => insertInventoryItemSchema,
  insertJournalEntryLineSchema: () => insertJournalEntryLineSchema,
  insertJournalEntrySchema: () => insertJournalEntrySchema,
  insertPayrollRecordSchema: () => insertPayrollRecordSchema,
  insertPurchaseInvoiceItemSchema: () => insertPurchaseInvoiceItemSchema,
  insertPurchaseInvoiceSchema: () => insertPurchaseInvoiceSchema,
  insertSalesInvoiceItemSchema: () => insertSalesInvoiceItemSchema,
  insertSalesInvoiceSchema: () => insertSalesInvoiceSchema,
  insertServiceSchema: () => insertServiceSchema,
  insertStockMovementSchema: () => insertStockMovementSchema,
  insertSupplierSchema: () => insertSupplierSchema,
  insertUserSchema: () => insertUserSchema,
  insertWarehouseSchema: () => insertWarehouseSchema,
  inventoryItems: () => inventoryItems,
  inventoryItemsRelations: () => inventoryItemsRelations,
  journalEntries: () => journalEntries,
  journalEntriesRelations: () => journalEntriesRelations,
  journalEntryLines: () => journalEntryLines,
  journalEntryLinesRelations: () => journalEntryLinesRelations,
  payrollRecords: () => payrollRecords,
  payrollRecordsRelations: () => payrollRecordsRelations,
  purchaseInvoiceItems: () => purchaseInvoiceItems,
  purchaseInvoiceItemsRelations: () => purchaseInvoiceItemsRelations,
  purchaseInvoices: () => purchaseInvoices,
  purchaseInvoicesRelations: () => purchaseInvoicesRelations,
  salesInvoiceItems: () => salesInvoiceItems,
  salesInvoiceItemsRelations: () => salesInvoiceItemsRelations,
  salesInvoices: () => salesInvoices,
  salesInvoicesRelations: () => salesInvoicesRelations,
  services: () => services,
  servicesRelations: () => servicesRelations,
  stockMovements: () => stockMovements,
  stockMovementsRelations: () => stockMovementsRelations,
  suppliers: () => suppliers,
  suppliersRelations: () => suppliersRelations,
  users: () => users,
  warehouses: () => warehouses,
  warehousesRelations: () => warehousesRelations
});
import { sqliteTable, text, integer, real } from "drizzle-orm/sqlite-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
var companies = sqliteTable("companies", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  address: text("address"),
  phone: text("phone"),
  email: text("email"),
  taxNumber: text("tax_number"),
  logoUrl: text("logo_url"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var customers = sqliteTable("customers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var suppliers = sqliteTable("suppliers", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  phone: text("phone"),
  address: text("address"),
  taxNumber: text("tax_number"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var services = sqliteTable("services", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  price: real("price").notNull(),
  unit: text("unit").default("sq.m"),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var salesInvoices = sqliteTable("sales_invoices", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceNumber: text("invoice_number").notNull().unique(),
  customerId: integer("customer_id").references(() => customers.id).notNull(),
  invoiceDate: integer("invoice_date", { mode: "timestamp" }).notNull(),
  subtotal: real("subtotal").notNull(),
  discountPercent: real("discount_percent").default(0),
  discountAmount: real("discount_amount").default(0),
  taxRate: real("tax_rate").default(0),
  taxAmount: real("tax_amount").default(0),
  total: real("total").notNull(),
  notes: text("notes"),
  status: text("status").default("draft"),
  // draft, sent, paid, cancelled
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var salesInvoiceItems = sqliteTable("sales_invoice_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceId: integer("invoice_id").references(() => salesInvoices.id).notNull(),
  serviceId: integer("service_id").references(() => services.id).notNull(),
  description: text("description"),
  quantity: real("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var purchaseInvoices = sqliteTable("purchase_invoices", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceNumber: text("invoice_number").notNull().unique(),
  supplierId: integer("supplier_id").references(() => suppliers.id).notNull(),
  invoiceDate: integer("invoice_date", { mode: "timestamp" }).notNull(),
  subtotal: real("subtotal").notNull(),
  discountPercent: real("discount_percent").default(0),
  discountAmount: real("discount_amount").default(0),
  taxRate: real("tax_rate").default(0),
  taxAmount: real("tax_amount").default(0),
  total: real("total").notNull(),
  notes: text("notes"),
  status: text("status").default("pending"),
  // pending, paid, cancelled
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var purchaseInvoiceItems = sqliteTable("purchase_invoice_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  invoiceId: integer("invoice_id").references(() => purchaseInvoices.id).notNull(),
  description: text("description").notNull(),
  quantity: real("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  total: real("total").notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var employees = sqliteTable("employees", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  position: text("position"),
  branch: text("branch"),
  basicSalary: real("basic_salary"),
  workingHours: integer("working_hours").default(8),
  phone: text("phone"),
  address: text("address"),
  hireDate: integer("hire_date", { mode: "timestamp" }),
  active: integer("active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var payrollRecords = sqliteTable("payroll_records", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  employeeId: integer("employee_id").references(() => employees.id).notNull(),
  payPeriodStart: integer("pay_period_start", { mode: "timestamp" }).notNull(),
  payPeriodEnd: integer("pay_period_end", { mode: "timestamp" }).notNull(),
  basicPay: real("basic_pay").notNull(),
  overtime: real("overtime").default(0),
  bonuses: real("bonuses").default(0),
  deductions: real("deductions").default(0),
  grossPay: real("gross_pay").notNull(),
  netPay: real("net_pay").notNull(),
  status: text("status").default("pending"),
  // pending, paid
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var warehouses = sqliteTable("warehouses", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  location: text("location"),
  description: text("description"),
  active: integer("active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var inventoryItems = sqliteTable("inventory_items", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  code: text("code").notNull().unique(),
  name: text("name").notNull(),
  description: text("description"),
  category: text("category"),
  unit: text("unit").default("piece"),
  costPrice: real("cost_price"),
  sellingPrice: real("selling_price"),
  minStock: real("min_stock").default(0),
  maxStock: real("max_stock").default(0),
  currentStock: real("current_stock").default(0),
  active: integer("active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var stockMovements = sqliteTable("stock_movements", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  itemId: integer("item_id").references(() => inventoryItems.id).notNull(),
  warehouseId: integer("warehouse_id").references(() => warehouses.id).notNull(),
  movementType: text("movement_type").notNull(),
  // in, out, transfer, adjustment
  quantity: real("quantity").notNull(),
  unitCost: real("unit_cost"),
  totalCost: real("total_cost"),
  referenceType: text("reference_type"),
  // sales_invoice, purchase_invoice, adjustment
  referenceId: integer("reference_id"),
  notes: text("notes"),
  movementDate: integer("movement_date", { mode: "timestamp" }).notNull(),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var chartOfAccounts = sqliteTable("chart_of_accounts", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  accountCode: text("account_code").notNull().unique(),
  accountName: text("account_name").notNull(),
  accountType: text("account_type").notNull(),
  // asset, liability, equity, revenue, expense
  parentId: integer("parent_id").references(() => chartOfAccounts.id),
  level: integer("level").default(1),
  active: integer("active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var journalEntries = sqliteTable("journal_entries", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  entryNumber: text("entry_number").notNull().unique(),
  entryDate: integer("entry_date", { mode: "timestamp" }).notNull(),
  description: text("description").notNull(),
  referenceType: text("reference_type"),
  // sales_invoice, purchase_invoice, manual
  referenceId: integer("reference_id"),
  totalDebit: real("total_debit").notNull(),
  totalCredit: real("total_credit").notNull(),
  status: text("status").default("draft"),
  // draft, posted
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var journalEntryLines = sqliteTable("journal_entry_lines", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  entryId: integer("entry_id").references(() => journalEntries.id).notNull(),
  accountId: integer("account_id").references(() => chartOfAccounts.id).notNull(),
  description: text("description"),
  debitAmount: real("debit_amount").default(0),
  creditAmount: real("credit_amount").default(0),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").default("user"),
  active: integer("active", { mode: "boolean" }).default(true),
  createdAt: integer("created_at", { mode: "timestamp" }).$defaultFn(() => /* @__PURE__ */ new Date())
});
var customersRelations = relations(customers, ({ many }) => ({
  salesInvoices: many(salesInvoices),
  stockMovements: many(stockMovements)
}));
var suppliersRelations = relations(suppliers, ({ many }) => ({
  purchaseInvoices: many(purchaseInvoices),
  stockMovements: many(stockMovements)
}));
var servicesRelations = relations(services, ({ many }) => ({
  salesInvoiceItems: many(salesInvoiceItems)
}));
var salesInvoicesRelations = relations(salesInvoices, ({ one, many }) => ({
  customer: one(customers, {
    fields: [salesInvoices.customerId],
    references: [customers.id]
  }),
  items: many(salesInvoiceItems)
}));
var salesInvoiceItemsRelations = relations(salesInvoiceItems, ({ one }) => ({
  invoice: one(salesInvoices, {
    fields: [salesInvoiceItems.invoiceId],
    references: [salesInvoices.id]
  }),
  service: one(services, {
    fields: [salesInvoiceItems.serviceId],
    references: [services.id]
  })
}));
var purchaseInvoicesRelations = relations(purchaseInvoices, ({ one, many }) => ({
  supplier: one(suppliers, {
    fields: [purchaseInvoices.supplierId],
    references: [suppliers.id]
  }),
  items: many(purchaseInvoiceItems)
}));
var purchaseInvoiceItemsRelations = relations(purchaseInvoiceItems, ({ one }) => ({
  invoice: one(purchaseInvoices, {
    fields: [purchaseInvoiceItems.invoiceId],
    references: [purchaseInvoices.id]
  })
}));
var employeesRelations = relations(employees, ({ many }) => ({
  payrollRecords: many(payrollRecords)
}));
var payrollRecordsRelations = relations(payrollRecords, ({ one }) => ({
  employee: one(employees, {
    fields: [payrollRecords.employeeId],
    references: [employees.id]
  })
}));
var warehousesRelations = relations(warehouses, ({ many }) => ({
  stockMovements: many(stockMovements)
}));
var inventoryItemsRelations = relations(inventoryItems, ({ many }) => ({
  stockMovements: many(stockMovements)
}));
var stockMovementsRelations = relations(stockMovements, ({ one }) => ({
  item: one(inventoryItems, {
    fields: [stockMovements.itemId],
    references: [inventoryItems.id]
  }),
  warehouse: one(warehouses, {
    fields: [stockMovements.warehouseId],
    references: [warehouses.id]
  })
}));
var chartOfAccountsRelations = relations(chartOfAccounts, ({ one, many }) => ({
  parent: one(chartOfAccounts, {
    fields: [chartOfAccounts.parentId],
    references: [chartOfAccounts.id]
  }),
  children: many(chartOfAccounts),
  journalEntryLines: many(journalEntryLines)
}));
var journalEntriesRelations = relations(journalEntries, ({ many }) => ({
  lines: many(journalEntryLines)
}));
var journalEntryLinesRelations = relations(journalEntryLines, ({ one }) => ({
  entry: one(journalEntries, {
    fields: [journalEntryLines.entryId],
    references: [journalEntries.id]
  }),
  account: one(chartOfAccounts, {
    fields: [journalEntryLines.accountId],
    references: [chartOfAccounts.id]
  })
}));
var insertCompanySchema = createInsertSchema(companies);
var insertCustomerSchema = createInsertSchema(customers);
var insertSupplierSchema = createInsertSchema(suppliers);
var insertServiceSchema = createInsertSchema(services);
var insertSalesInvoiceSchema = createInsertSchema(salesInvoices);
var insertSalesInvoiceItemSchema = createInsertSchema(salesInvoiceItems);
var insertPurchaseInvoiceSchema = createInsertSchema(purchaseInvoices);
var insertPurchaseInvoiceItemSchema = createInsertSchema(purchaseInvoiceItems);
var insertEmployeeSchema = createInsertSchema(employees);
var insertPayrollRecordSchema = createInsertSchema(payrollRecords);
var insertWarehouseSchema = createInsertSchema(warehouses);
var insertInventoryItemSchema = createInsertSchema(inventoryItems);
var insertStockMovementSchema = createInsertSchema(stockMovements);
var insertChartOfAccountSchema = createInsertSchema(chartOfAccounts);
var insertJournalEntrySchema = createInsertSchema(journalEntries);
var insertJournalEntryLineSchema = createInsertSchema(journalEntryLines);
var insertUserSchema = createInsertSchema(users);

// server/db.ts
import { join } from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { existsSync, mkdirSync } from "fs";
var __filename = fileURLToPath(import.meta.url);
var __dirname = dirname(__filename);
var dataDir = join(__dirname, "../data");
if (!existsSync(dataDir)) {
  mkdirSync(dataDir, { recursive: true });
}
var dbPath = join(dataDir, "glass-factory-erp.db");
var sqlite = new Database(dbPath);
sqlite.pragma("journal_mode = WAL");
var db = drizzle(sqlite, { schema: schema_exports });
var initializeDatabase = async () => {
  try {
    const createTablesSQL = `
      CREATE TABLE IF NOT EXISTS companies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        email TEXT,
        tax_number TEXT,
        logo_url TEXT,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        phone TEXT,
        address TEXT,
        tax_number TEXT,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        phone TEXT,
        address TEXT,
        tax_number TEXT,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS services (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        unit TEXT DEFAULT 'sq.m',
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS sales_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_id INTEGER NOT NULL REFERENCES customers(id),
        invoice_date INTEGER NOT NULL,
        subtotal REAL NOT NULL,
        discount_percent REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        tax_rate REAL DEFAULT 0,
        tax_amount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        status TEXT DEFAULT 'draft',
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS sales_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL REFERENCES sales_invoices(id),
        service_id INTEGER NOT NULL REFERENCES services(id),
        description TEXT,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total REAL NOT NULL,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS purchase_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        supplier_id INTEGER NOT NULL REFERENCES suppliers(id),
        invoice_date INTEGER NOT NULL,
        subtotal REAL NOT NULL,
        discount_percent REAL DEFAULT 0,
        discount_amount REAL DEFAULT 0,
        tax_rate REAL DEFAULT 0,
        tax_amount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        status TEXT DEFAULT 'pending',
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS purchase_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL REFERENCES purchase_invoices(id),
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        total REAL NOT NULL,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS employees (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        position TEXT,
        branch TEXT,
        basic_salary REAL,
        working_hours INTEGER DEFAULT 8,
        phone TEXT,
        address TEXT,
        hire_date INTEGER,
        active INTEGER DEFAULT 1,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS payroll_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_id INTEGER NOT NULL REFERENCES employees(id),
        pay_period_start INTEGER NOT NULL,
        pay_period_end INTEGER NOT NULL,
        basic_pay REAL NOT NULL,
        overtime REAL DEFAULT 0,
        bonuses REAL DEFAULT 0,
        deductions REAL DEFAULT 0,
        gross_pay REAL NOT NULL,
        net_pay REAL NOT NULL,
        status TEXT DEFAULT 'pending',
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS warehouses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        location TEXT,
        description TEXT,
        active INTEGER DEFAULT 1,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS inventory_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        category TEXT,
        unit TEXT DEFAULT 'piece',
        cost_price REAL,
        selling_price REAL,
        min_stock REAL DEFAULT 0,
        max_stock REAL DEFAULT 0,
        current_stock REAL DEFAULT 0,
        active INTEGER DEFAULT 1,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS stock_movements (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id INTEGER NOT NULL REFERENCES inventory_items(id),
        warehouse_id INTEGER NOT NULL REFERENCES warehouses(id),
        movement_type TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_cost REAL,
        total_cost REAL,
        reference_type TEXT,
        reference_id INTEGER,
        notes TEXT,
        movement_date INTEGER NOT NULL,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_code TEXT NOT NULL UNIQUE,
        account_name TEXT NOT NULL,
        account_type TEXT NOT NULL,
        parent_id INTEGER REFERENCES chart_of_accounts(id),
        level INTEGER DEFAULT 1,
        active INTEGER DEFAULT 1,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS journal_entries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_number TEXT NOT NULL UNIQUE,
        entry_date INTEGER NOT NULL,
        description TEXT NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        total_debit REAL NOT NULL,
        total_credit REAL NOT NULL,
        status TEXT DEFAULT 'draft',
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS journal_entry_lines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        entry_id INTEGER NOT NULL REFERENCES journal_entries(id),
        account_id INTEGER NOT NULL REFERENCES chart_of_accounts(id),
        description TEXT,
        debit_amount REAL DEFAULT 0,
        credit_amount REAL DEFAULT 0,
        created_at INTEGER
      );

      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        active INTEGER DEFAULT 1,
        created_at INTEGER
      );
    `;
    sqlite.exec(createTablesSQL);
    console.log("Database initialized successfully");
  } catch (error) {
    console.error("Failed to initialize database:", error);
    throw error;
  }
};

// server/storage.ts
import { eq, desc, and, or, sql, gte } from "drizzle-orm";
var DatabaseStorage = class {
  async getUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || void 0;
  }
  async getUserByUsername(username) {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || void 0;
  }
  async createUser(insertUser) {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }
  async getCompany() {
    const [company] = await db.select().from(companies).limit(1);
    return company || void 0;
  }
  async createOrUpdateCompany(company) {
    const existing = await this.getCompany();
    if (existing) {
      const [updated] = await db.update(companies).set(company).where(eq(companies.id, existing.id)).returning();
      return updated;
    } else {
      const [created] = await db.insert(companies).values(company).returning();
      return created;
    }
  }
  async getCustomers() {
    return await db.select().from(customers).orderBy(desc(customers.createdAt));
  }
  async getCustomer(id) {
    const [customer] = await db.select().from(customers).where(eq(customers.id, id));
    return customer || void 0;
  }
  async createCustomer(customer) {
    const count = await db.select({ count: sql`count(*)` }).from(customers);
    const customerCount = Number(count[0].count) + 1;
    const code = `CUST-${customerCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(customers).values({ ...customer, code }).returning();
    return created;
  }
  async updateCustomer(id, customer) {
    const [updated] = await db.update(customers).set(customer).where(eq(customers.id, id)).returning();
    return updated;
  }
  async deleteCustomer(id) {
    await db.delete(customers).where(eq(customers.id, id));
  }
  async getSuppliers() {
    return await db.select().from(suppliers).orderBy(desc(suppliers.createdAt));
  }
  async getSupplier(id) {
    const [supplier] = await db.select().from(suppliers).where(eq(suppliers.id, id));
    return supplier || void 0;
  }
  async createSupplier(supplier) {
    const count = await db.select({ count: sql`count(*)` }).from(suppliers);
    const supplierCount = Number(count[0].count) + 1;
    const code = `SUPP-${supplierCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(suppliers).values({ ...supplier, code }).returning();
    return created;
  }
  async updateSupplier(id, supplier) {
    const [updated] = await db.update(suppliers).set(supplier).where(eq(suppliers.id, id)).returning();
    return updated;
  }
  async deleteSupplier(id) {
    await db.delete(suppliers).where(eq(suppliers.id, id));
  }
  async getServices() {
    return await db.select().from(services).orderBy(desc(services.createdAt));
  }
  async getService(id) {
    const [service] = await db.select().from(services).where(eq(services.id, id));
    return service || void 0;
  }
  async createService(service) {
    const [created] = await db.insert(services).values(service).returning();
    return created;
  }
  async updateService(id, service) {
    const [updated] = await db.update(services).set(service).where(eq(services.id, id)).returning();
    return updated;
  }
  async deleteService(id) {
    await db.delete(services).where(eq(services.id, id));
  }
  async getSalesInvoices() {
    return await db.select().from(salesInvoices).leftJoin(customers, eq(salesInvoices.customerId, customers.id)).orderBy(desc(salesInvoices.createdAt)).then((results) => results.map((result) => ({
      ...result.sales_invoices,
      customer: result.customers
    })));
  }
  async getSalesInvoice(id) {
    const [invoice] = await db.select().from(salesInvoices).leftJoin(customers, eq(salesInvoices.customerId, customers.id)).where(eq(salesInvoices.id, id));
    if (!invoice) return void 0;
    const items = await db.select().from(salesInvoiceItems).leftJoin(services, eq(salesInvoiceItems.serviceId, services.id)).where(eq(salesInvoiceItems.invoiceId, id)).then((results) => results.map((result) => ({
      ...result.sales_invoice_items,
      service: result.services
    })));
    return {
      ...invoice.sales_invoices,
      customer: invoice.customers,
      items
    };
  }
  async createSalesInvoice(invoice, items) {
    const count = await db.select({ count: sql`count(*)` }).from(salesInvoices);
    const invoiceCount = Number(count[0].count) + 1;
    const invoiceNumber = `INV-${(/* @__PURE__ */ new Date()).getFullYear()}-${invoiceCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(salesInvoices).values({ ...invoice, invoiceNumber }).returning();
    const itemsWithInvoiceId = items.map((item) => ({ ...item, invoiceId: created.id }));
    await db.insert(salesInvoiceItems).values(itemsWithInvoiceId);
    return created;
  }
  async updateSalesInvoice(id, invoice) {
    const [updated] = await db.update(salesInvoices).set({ ...invoice, updatedAt: /* @__PURE__ */ new Date() }).where(eq(salesInvoices.id, id)).returning();
    return updated;
  }
  async deleteSalesInvoice(id) {
    await db.delete(salesInvoiceItems).where(eq(salesInvoiceItems.invoiceId, id));
    await db.delete(salesInvoices).where(eq(salesInvoices.id, id));
  }
  async getEmployees() {
    return await db.select().from(employees).orderBy(desc(employees.createdAt));
  }
  async getEmployee(id) {
    const [employee] = await db.select().from(employees).where(eq(employees.id, id));
    return employee || void 0;
  }
  async createEmployee(employee) {
    const count = await db.select({ count: sql`count(*)` }).from(employees);
    const employeeCount = Number(count[0].count) + 1;
    const code = `EMP-${employeeCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(employees).values({ ...employee, code }).returning();
    return created;
  }
  async updateEmployee(id, employee) {
    const [updated] = await db.update(employees).set(employee).where(eq(employees.id, id)).returning();
    return updated;
  }
  async deleteEmployee(id) {
    await db.delete(employees).where(eq(employees.id, id));
  }
  async getPayrollRecords() {
    return await db.select().from(payrollRecords).leftJoin(employees, eq(payrollRecords.employeeId, employees.id)).orderBy(desc(payrollRecords.createdAt)).then((results) => results.map((result) => ({
      ...result.payroll_records,
      employee: result.employees
    })));
  }
  async getPayrollRecord(id) {
    const [record] = await db.select().from(payrollRecords).leftJoin(employees, eq(payrollRecords.employeeId, employees.id)).where(eq(payrollRecords.id, id));
    if (!record) return void 0;
    return {
      ...record.payroll_records,
      employee: record.employees
    };
  }
  async createPayrollRecord(record) {
    const [created] = await db.insert(payrollRecords).values(record).returning();
    return created;
  }
  async updatePayrollRecord(id, record) {
    const [updated] = await db.update(payrollRecords).set(record).where(eq(payrollRecords.id, id)).returning();
    return updated;
  }
  async deletePayrollRecord(id) {
    await db.delete(payrollRecords).where(eq(payrollRecords.id, id));
  }
  async getWarehouses() {
    return await db.select().from(warehouses).orderBy(desc(warehouses.createdAt));
  }
  async getWarehouse(id) {
    const [warehouse] = await db.select().from(warehouses).where(eq(warehouses.id, id));
    return warehouse || void 0;
  }
  async createWarehouse(warehouse) {
    const count = await db.select({ count: sql`count(*)` }).from(warehouses);
    const warehouseCount = Number(count[0].count) + 1;
    const code = `WH-${warehouseCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(warehouses).values({ ...warehouse, code }).returning();
    return created;
  }
  async updateWarehouse(id, warehouse) {
    const [updated] = await db.update(warehouses).set(warehouse).where(eq(warehouses.id, id)).returning();
    return updated;
  }
  async deleteWarehouse(id) {
    await db.delete(warehouses).where(eq(warehouses.id, id));
  }
  async getInventoryItems() {
    return await db.select().from(inventoryItems).leftJoin(warehouses, eq(inventoryItems.warehouseId, warehouses.id)).orderBy(desc(inventoryItems.createdAt)).then((results) => results.map((result) => ({
      ...result.inventory_items,
      warehouse: result.warehouses
    })));
  }
  async getInventoryItem(id) {
    const [item] = await db.select().from(inventoryItems).leftJoin(warehouses, eq(inventoryItems.warehouseId, warehouses.id)).where(eq(inventoryItems.id, id));
    if (!item) return void 0;
    return {
      ...item.inventory_items,
      warehouse: item.warehouses
    };
  }
  async createInventoryItem(item) {
    const count = await db.select({ count: sql`count(*)` }).from(inventoryItems);
    const itemCount = Number(count[0].count) + 1;
    const code = `ITEM-${itemCount.toString().padStart(4, "0")}`;
    const [created] = await db.insert(inventoryItems).values({ ...item, code }).returning();
    return created;
  }
  async updateInventoryItem(id, item) {
    const [updated] = await db.update(inventoryItems).set(item).where(eq(inventoryItems.id, id)).returning();
    return updated;
  }
  async deleteInventoryItem(id) {
    await db.delete(inventoryItems).where(eq(inventoryItems.id, id));
  }
  async getStockMovements() {
    return await db.select().from(stockMovements).leftJoin(warehouses, eq(stockMovements.warehouseId, warehouses.id)).leftJoin(inventoryItems, eq(stockMovements.itemId, inventoryItems.id)).leftJoin(suppliers, eq(stockMovements.supplierId, suppliers.id)).leftJoin(customers, eq(stockMovements.customerId, customers.id)).orderBy(desc(stockMovements.createdAt)).then((results) => results.map((result) => ({
      ...result.stock_movements,
      warehouse: result.warehouses,
      item: result.inventory_items,
      supplier: result.suppliers || void 0,
      customer: result.customers || void 0
    })));
  }
  async createStockMovement(movement) {
    const [created] = await db.insert(stockMovements).values(movement).returning();
    return created;
  }
  async getDashboardStats() {
    const salesResult = await db.select({ total: sql`COALESCE(SUM(total), 0)` }).from(salesInvoices).where(and(
      eq(salesInvoices.status, "final"),
      gte(salesInvoices.invoiceDate, sql`date_trunc('month', current_date)`)
    ));
    const ordersResult = await db.select({ count: sql`count(*)` }).from(salesInvoices).where(or(
      eq(salesInvoices.status, "draft"),
      and(eq(salesInvoices.status, "final"), sql`balance > 0`)
    ));
    const inventoryResult = await db.select({ value: sql`COALESCE(SUM(total_value), 0)` }).from(inventoryItems);
    const employeesResult = await db.select({ count: sql`count(*)` }).from(employees).where(eq(employees.active, true));
    return {
      totalSales: salesResult[0].total?.toString() || "0",
      activeOrders: Number(ordersResult[0].count),
      inventoryValue: inventoryResult[0].value?.toString() || "0",
      employees: Number(employeesResult[0].count)
    };
  }
  async getRecentSalesInvoices(limit = 5) {
    return await db.select().from(salesInvoices).leftJoin(customers, eq(salesInvoices.customerId, customers.id)).orderBy(desc(salesInvoices.createdAt)).limit(limit).then((results) => results.map((result) => ({
      ...result.sales_invoices,
      customer: result.customers
    })));
  }
};
var storage = new DatabaseStorage();

// server/routes.ts
async function registerRoutes(app2) {
  app2.get("/api/company", async (req, res) => {
    try {
      const company = await storage.getCompany();
      res.json(company);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch company" });
    }
  });
  app2.post("/api/company", async (req, res) => {
    try {
      const data = insertCompanySchema.parse(req.body);
      const company = await storage.createOrUpdateCompany(data);
      res.json(company);
    } catch (error) {
      res.status(400).json({ error: "Invalid company data" });
    }
  });
  app2.get("/api/dashboard/stats", async (req, res) => {
    try {
      const stats = await storage.getDashboardStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch dashboard stats" });
    }
  });
  app2.get("/api/dashboard/recent-invoices", async (req, res) => {
    try {
      const invoices = await storage.getRecentSalesInvoices(5);
      res.json(invoices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch recent invoices" });
    }
  });
  app2.get("/api/customers", async (req, res) => {
    try {
      const customers2 = await storage.getCustomers();
      res.json(customers2);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch customers" });
    }
  });
  app2.get("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const customer = await storage.getCustomer(id);
      if (!customer) {
        return res.status(404).json({ error: "Customer not found" });
      }
      res.json(customer);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch customer" });
    }
  });
  app2.post("/api/customers", async (req, res) => {
    try {
      const data = insertCustomerSchema.parse(req.body);
      const customer = await storage.createCustomer(data);
      res.json(customer);
    } catch (error) {
      res.status(400).json({ error: "Invalid customer data" });
    }
  });
  app2.put("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = insertCustomerSchema.partial().parse(req.body);
      const customer = await storage.updateCustomer(id, data);
      res.json(customer);
    } catch (error) {
      res.status(400).json({ error: "Invalid customer data" });
    }
  });
  app2.delete("/api/customers/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteCustomer(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete customer" });
    }
  });
  app2.get("/api/services", async (req, res) => {
    try {
      const services2 = await storage.getServices();
      res.json(services2);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch services" });
    }
  });
  app2.post("/api/services", async (req, res) => {
    try {
      const data = insertServiceSchema.parse(req.body);
      const service = await storage.createService(data);
      res.json(service);
    } catch (error) {
      res.status(400).json({ error: "Invalid service data" });
    }
  });
  app2.put("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = insertServiceSchema.partial().parse(req.body);
      const service = await storage.updateService(id, data);
      res.json(service);
    } catch (error) {
      res.status(400).json({ error: "Invalid service data" });
    }
  });
  app2.delete("/api/services/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteService(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete service" });
    }
  });
  app2.get("/api/sales/invoices", async (req, res) => {
    try {
      const invoices = await storage.getSalesInvoices();
      res.json(invoices);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch sales invoices" });
    }
  });
  app2.get("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const invoice = await storage.getSalesInvoice(id);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch invoice" });
    }
  });
  app2.post("/api/sales/invoices", async (req, res) => {
    try {
      const { invoice, items } = req.body;
      const invoiceData = insertSalesInvoiceSchema.parse(invoice);
      const itemsData = items.map((item) => insertSalesInvoiceItemSchema.parse(item));
      const createdInvoice = await storage.createSalesInvoice(invoiceData, itemsData);
      res.json(createdInvoice);
    } catch (error) {
      res.status(400).json({ error: "Invalid invoice data" });
    }
  });
  app2.put("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = insertSalesInvoiceSchema.partial().parse(req.body);
      const invoice = await storage.updateSalesInvoice(id, data);
      res.json(invoice);
    } catch (error) {
      res.status(400).json({ error: "Invalid invoice data" });
    }
  });
  app2.delete("/api/sales/invoices/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteSalesInvoice(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete invoice" });
    }
  });
  app2.get("/api/employees", async (req, res) => {
    try {
      const employees2 = await storage.getEmployees();
      res.json(employees2);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch employees" });
    }
  });
  app2.post("/api/employees", async (req, res) => {
    try {
      const data = insertEmployeeSchema.parse(req.body);
      const employee = await storage.createEmployee(data);
      res.json(employee);
    } catch (error) {
      res.status(400).json({ error: "Invalid employee data" });
    }
  });
  app2.put("/api/employees/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const data = insertEmployeeSchema.partial().parse(req.body);
      const employee = await storage.updateEmployee(id, data);
      res.json(employee);
    } catch (error) {
      res.status(400).json({ error: "Invalid employee data" });
    }
  });
  app2.get("/api/warehouses", async (req, res) => {
    try {
      const warehouses2 = await storage.getWarehouses();
      res.json(warehouses2);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch warehouses" });
    }
  });
  app2.post("/api/warehouses", async (req, res) => {
    try {
      const data = insertWarehouseSchema.parse(req.body);
      const warehouse = await storage.createWarehouse(data);
      res.json(warehouse);
    } catch (error) {
      res.status(400).json({ error: "Invalid warehouse data" });
    }
  });
  app2.get("/api/inventory/items", async (req, res) => {
    try {
      const items = await storage.getInventoryItems();
      res.json(items);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch inventory items" });
    }
  });
  app2.post("/api/inventory/items", async (req, res) => {
    try {
      const data = insertInventoryItemSchema.parse(req.body);
      const item = await storage.createInventoryItem(data);
      res.json(item);
    } catch (error) {
      res.status(400).json({ error: "Invalid inventory item data" });
    }
  });
  app2.get("/api/inventory/movements", async (req, res) => {
    try {
      const movements = await storage.getStockMovements();
      res.json(movements);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch stock movements" });
    }
  });
  app2.post("/api/inventory/movements", async (req, res) => {
    try {
      const data = insertStockMovementSchema.parse(req.body);
      const movement = await storage.createStockMovement(data);
      res.json(movement);
    } catch (error) {
      res.status(400).json({ error: "Invalid stock movement data" });
    }
  });
  const httpServer = createServer(app2);
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: void 0
      }
    }
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  try {
    await initializeDatabase();
    log("Database initialized successfully");
  } catch (error) {
    log("Failed to initialize database:", error);
    process.exit(1);
  }
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
