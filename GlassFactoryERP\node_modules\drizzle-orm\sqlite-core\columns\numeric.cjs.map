{"version": 3, "sources": ["../../../src/sqlite-core/columns/numeric.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteNumericBuilderInitial<TName extends string> = SQLiteNumericBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteNumeric';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteNumericBuilder<T extends ColumnBuilderBaseConfig<'string', 'SQLiteNumeric'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteNumericBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SQLiteNumeric');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteNumeric<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteNumeric<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteNumeric<T extends ColumnBaseConfig<'string', 'SQLiteNumeric'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteNumeric';\n\n\tgetSQLType(): string {\n\t\treturn 'numeric';\n\t}\n}\n\nexport function numeric(): SQLiteNumericBuilderInitial<''>;\nexport function numeric<TName extends string>(name: TName): SQLiteNumericBuilderInitial<TName>;\nexport function numeric(name?: string) {\n\treturn new SQLiteNumericBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkD;AAW3C,MAAM,6BACJ,kCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,eAAe;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA6E,2BAAgB;AAAA,EACzG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,QAAQ,MAAe;AACtC,SAAO,IAAI,qBAAqB,QAAQ,EAAE;AAC3C;", "names": []}