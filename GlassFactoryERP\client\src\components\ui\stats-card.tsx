import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string;
  change?: string;
  changeType?: "positive" | "negative" | "neutral";
  subtitle?: string;
  icon: LucideIcon;
  iconColor?: "emerald" | "blue" | "purple" | "amber" | "red" | "gray";
}

const iconColorClasses = {
  emerald: "bg-emerald-100 text-emerald-600",
  blue: "bg-blue-100 text-blue-600",
  purple: "bg-purple-100 text-purple-600",
  amber: "bg-amber-100 text-amber-600",
  red: "bg-red-100 text-red-600",
  gray: "bg-gray-100 text-gray-600",
};

const changeColorClasses = {
  positive: "text-emerald-600",
  negative: "text-red-600",
  neutral: "text-slate-600",
};

export default function StatsCard({ 
  title, 
  value, 
  change, 
  changeType = "neutral", 
  subtitle, 
  icon: Icon, 
  iconColor = "blue" 
}: StatsCardProps) {
  return (
    <Card className="bg-white rounded-xl shadow-sm border border-slate-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-slate-600 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-slate-800">{value}</p>
            {change && (
              <p className={cn("text-sm font-medium", changeColorClasses[changeType])}>
                {change}
              </p>
            )}
            {subtitle && (
              <p className="text-slate-600 text-sm font-medium">{subtitle}</p>
            )}
          </div>
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center",
            iconColorClasses[iconColor]
          )}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
