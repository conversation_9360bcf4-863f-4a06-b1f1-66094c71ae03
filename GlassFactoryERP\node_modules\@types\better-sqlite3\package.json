{"name": "@types/better-sqlite3", "version": "7.6.13", "description": "TypeScript definitions for better-sqlite3", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/better-sqlite3", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Morfent"}, {"name": "<PERSON><PERSON>", "githubUsername": "matrumz", "url": "https://github.com/matrumz"}, {"name": "Santiago Aguilar", "githubUsername": "sant123", "url": "https://github.com/sant123"}, {"name": "<PERSON>", "githubUsername": "loghorn", "url": "https://github.com/loghorn"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/andykais"}, {"name": "<PERSON>", "githubUsername": "mrkstwrt", "url": "https://github.com/mrkstwrt"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "stamerf", "url": "https://github.com/stamerf"}, {"name": "<PERSON><PERSON>", "githubUsername": "beenotung", "url": "https://github.com/beenotung"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/better-sqlite3"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d3573b172a1ed98c5e63d112a1d270bef2b683e7f13829155088694326b5fbac", "typeScriptVersion": "5.1"}