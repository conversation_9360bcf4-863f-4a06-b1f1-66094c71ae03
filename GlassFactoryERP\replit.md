# Glass Factory ERP System

## Overview

This is a comprehensive web-based ERP system designed specifically for glass manufacturing factories. The system manages accounting, sales, purchases, payroll, and inventory operations with a focus on glass production workflows including square meter calculations for glass products.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite with hot module replacement
- **UI Library**: Radix UI components with shadcn/ui styling
- **Styling**: Tailwind CSS with custom design tokens
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: Wouter for client-side routing
- **Form Handling**: React Hook Form with Zod validation

### Backend Architecture
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js for REST API
- **Database**: PostgreSQL with Neon serverless hosting
- **ORM**: Drizzle ORM for type-safe database operations
- **Schema**: Shared TypeScript schemas between client and server
- **Session Management**: Express sessions with PostgreSQL store

### Database Design
- **ORM**: Drizzle with PostgreSQL dialect
- **Schema Location**: `shared/schema.ts` for type safety across frontend/backend
- **Migration Strategy**: Drizzle Kit for schema migrations
- **Connection**: Neon serverless PostgreSQL with connection pooling

## Key Components

### Core Modules

1. **Company Settings Module**
   - Company profile management
   - Logo upload and display across interfaces
   - Basic company information storage

2. **Sales Module**
   - Customer management with tax number support
   - Service catalog with pricing
   - Invoice generation with automatic square meter calculations
   - Invoice status workflow (Draft → Final → Cancelled)
   - Automatic accounting entry generation
   - Payment tracking and balance management

3. **Purchases Module**
   - Supplier management
   - Automated purchase invoice creation
   - Payment processing and account statements
   - PDF generation and printing capabilities
   - Inventory integration

4. **Payroll Module**
   - Employee registration with auto-generated codes
   - Comprehensive payroll operations (salaries, advances, bonuses, deductions)
   - Automatic accounting entries for payroll transactions
   - Configurable account mapping for different operations

5. **Inventory Module**
   - Stock level monitoring
   - Warehouse management
   - Movement tracking
   - Low stock alerts

6. **Accounting Module**
   - Chart of accounts management
   - Journal entry automation
   - Financial reporting
   - Configurable account mappings for different modules

### Glass Industry Specific Features
- **Square Meter Calculations**: Automatic calculation using length × width × quantity ÷ 1,000,000
- **Glass Service Management**: Specialized service catalog for glass products
- **Invoice Templates**: Industry-specific invoice layouts with glass measurements

## Data Flow

### Invoice Processing Flow
1. Create draft invoice with line items
2. Calculate square meters automatically for each line
3. Apply discounts and taxes
4. Generate final invoice with accounting entries
5. Track payments and update balances
6. Handle cancellations with reversal entries

### Accounting Integration
- Automatic journal entries for sales, purchases, and payroll
- Configurable account mapping for different transaction types
- Real-time balance updates across modules
- Audit trail for all financial transactions

### User Interface Flow
- Sidebar navigation with modular organization
- Dashboard with key performance indicators
- Modal-based forms for data entry
- Toast notifications for user feedback
- Responsive design for various screen sizes

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **drizzle-orm**: Type-safe database operations
- **@tanstack/react-query**: Server state management
- **@radix-ui/***: Accessible UI components
- **wouter**: Lightweight routing
- **react-hook-form**: Form management
- **zod**: Schema validation

### Development Tools
- **TypeScript**: Type safety across the stack
- **Vite**: Fast development and build process
- **Tailwind CSS**: Utility-first styling
- **esbuild**: Production build optimization

### Third-party Services
- **Neon Database**: Serverless PostgreSQL hosting
- **Replit**: Development and deployment platform

## Deployment Strategy

### Development Environment
- **Dev Server**: Vite development server with HMR
- **Database**: Neon PostgreSQL with environment-based connection
- **Build Process**: TypeScript compilation with Vite bundling

### Production Build
- **Frontend**: Static assets built with Vite
- **Backend**: Node.js server with esbuild bundling
- **Database**: Production Neon PostgreSQL instance
- **Environment**: Environment variables for database connection

### Database Management
- **Schema Migrations**: Drizzle Kit push/migrate commands
- **Connection Pooling**: Neon serverless connection management
- **Type Safety**: Shared schema definitions between client/server

## Changelog
```
Changelog:
- July 03, 2025. Initial setup
```

## User Preferences
```
Preferred communication style: Simple, everyday language.
```