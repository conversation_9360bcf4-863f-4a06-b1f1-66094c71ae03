import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Define resources inline to avoid TypeScript module resolution issues
const resources = {
  en: {
    translation: {
      nav: {
        main: "Main",
        operations: "Operations",
        humanResources: "Human Resources",
        finance: "Finance",
        dashboard: "Dashboard",
        companySettings: "Company Settings",
        sales: "Sales",
        purchases: "Purchases",
        inventory: "Inventory",
        manufacturing: "Manufacturing",
        payroll: "Payroll",
        accounting: "Accounting",
        reports: "Reports"
      },
      dashboard: {
        title: "Dashboard",
        subtitle: "Welcome back to your ERP system",
        totalSales: "Total Sales",
        activeOrders: "Active Orders",
        inventoryValue: "Inventory Value",
        employees: "Employees"
      },
      common: {
        save: "Save",
        cancel: "Cancel",
        edit: "Edit",
        delete: "Delete",
        view: "View",
        add: "Add",
        create: "Create",
        loading: "Loading..."
      }
    }
  },
  ar: {
    translation: {
      nav: {
        main: "الرئيسية",
        operations: "العمليات",
        humanResources: "الموارد البشرية",
        finance: "المالية",
        dashboard: "لوحة التحكم",
        companySettings: "إعدادات الشركة",
        sales: "المبيعات",
        purchases: "المشتريات",
        inventory: "المخزون",
        manufacturing: "التصنيع",
        payroll: "كشف المرتبات",
        accounting: "المحاسبة",
        reports: "التقارير"
      },
      dashboard: {
        title: "لوحة التحكم",
        subtitle: "مرحباً بك في نظام تخطيط موارد المؤسسة",
        totalSales: "إجمالي المبيعات",
        activeOrders: "الطلبات النشطة",
        inventoryValue: "قيمة المخزون",
        employees: "الموظفون"
      },
      common: {
        save: "حفظ",
        cancel: "إلغاء",
        edit: "تعديل",
        delete: "حذف",
        view: "عرض",
        add: "إضافة",
        create: "إنشاء",
        loading: "جاري التحميل..."
      }
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    lng: 'en', // Default language
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    
    // Keep numbers in English regardless of language
    returnNull: false,
    returnEmptyString: false,
  });

export default i18n;