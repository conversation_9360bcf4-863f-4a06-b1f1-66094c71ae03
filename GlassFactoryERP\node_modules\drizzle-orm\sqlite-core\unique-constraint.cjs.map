{"version": 3, "sources": ["../../src/sqlite-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { SQLiteColumn } from './columns/common.ts';\nimport type { SQLiteTable } from './table.ts';\n\nexport function uniqueKeyName(table: SQLiteTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: SQLiteColumn[];\n\n\tconstructor(\n\t\tcolumns: SQLiteColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\t/** @internal */\n\tbuild(table: SQLiteTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [SQLiteColumn, ...SQLiteColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'SQLiteUniqueConstraint';\n\n\treadonly columns: SQLiteColumn[];\n\treadonly name?: string;\n\n\tconstructor(readonly table: SQLiteTable, columns: SQLiteColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,yBAA0B;AAInB,SAAS,cAAc,OAAoB,SAAmB;AACpE,SAAO,GAAG,MAAM,4BAAS,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAChD;AAEO,SAAS,OAAO,MAA0C;AAChE,SAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,MAAM,wBAAwB;AAAA,EAMpC,YACC,SACQ,MACP;AADO;AAER,SAAK,UAAU;AAAA,EAChB;AAAA,EAVA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAUA,MAAM,OAAsC;AAC3C,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EAC3D;AACD;AAEO,MAAM,0BAA0B;AAAA,EACtC,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,MACC;AACD,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,MAAM,SAA4C;AACjD,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;AAAA,EACtD;AACD;AAEO,MAAM,iBAAiB;AAAA,EAM7B,YAAqB,OAAoB,SAAyB,MAAe;AAA5D;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AAAA,EACxF;AAAA,EARA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EAOT,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}