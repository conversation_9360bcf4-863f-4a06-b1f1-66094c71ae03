import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { formatCurrency, formatDate, getStatusColor } from "@/lib/utils";
import { 
  Users, 
  DollarSign, 
  Calculator, 
  FileText,
  Plus,
  Search,
  Edit,
  Trash2,
  Settings
} from "lucide-react";

export default function PayrollIndex() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"employees" | "payroll">("employees");

  const { data: employees, isLoading: employeesLoading } = useQuery({
    queryKey: ["/api/employees"],
  });

  const { data: payrollRecords, isLoading: payrollLoading } = useQuery({
    queryKey: ["/api/payroll"],
  });

  const filteredEmployees = employees?.filter((employee: any) =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (employeesLoading || payrollLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Payroll Management</h2>
        <p className="text-slate-600">Manage employees, salaries, and payroll operations</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Employees</p>
                <p className="text-2xl font-bold text-slate-800">{employees?.length || 0}</p>
                <p className="text-emerald-600 text-sm font-medium">Active workforce</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Monthly Payroll</p>
                <p className="text-2xl font-bold text-slate-800">$85,420</p>
                <p className="text-slate-600 text-sm font-medium">Current month</p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Avg. Salary</p>
                <p className="text-2xl font-bold text-slate-800">$3,800</p>
                <p className="text-slate-600 text-sm font-medium">Per employee</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calculator className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Pending</p>
                <p className="text-2xl font-bold text-slate-800">3</p>
                <p className="text-amber-600 text-sm font-medium">Awaiting processing</p>
              </div>
              <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        <Button
          variant={activeTab === "employees" ? "default" : "outline"}
          onClick={() => setActiveTab("employees")}
        >
          Employees
        </Button>
        <Button
          variant={activeTab === "payroll" ? "default" : "outline"}
          onClick={() => setActiveTab("payroll")}
        >
          Payroll Records
        </Button>
      </div>

      {/* Search and Actions */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={activeTab === "employees" ? "Search employees..." : "Search payroll records..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              {activeTab === "employees" && (
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Employee
                </Button>
              )}
              {activeTab === "payroll" && (
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Process Payroll
                </Button>
              )}
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content based on active tab */}
      {activeTab === "employees" ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Employees</span>
              <Badge variant="secondary">{filteredEmployees?.length || 0}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Code</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Name</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Position</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Branch</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Basic Salary</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Working Hours</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {filteredEmployees?.length ? (
                    filteredEmployees.map((employee: any) => (
                      <tr key={employee.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {employee.code}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.position || "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.branch || "-"}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {employee.basicSalary ? formatCurrency(employee.basicSalary) : "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.workingHours || 8} hours
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={getStatusColor(employee.active ? "active" : "inactive")}>
                            {employee.active ? "Active" : "Inactive"}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center space-y-3">
                          <Users className="h-12 w-12 text-gray-400" />
                          <div>
                            <p className="text-gray-500 font-medium">No employees found</p>
                            <p className="text-gray-400 text-sm">Add employees to get started with payroll</p>
                          </div>
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Employee
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Payroll Records</span>
              <Badge variant="secondary">{payrollRecords?.length || 0}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Employee</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Pay Date</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Base Salary</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Overtime</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Bonuses</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Deductions</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Net Pay</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {payrollRecords?.length ? (
                    payrollRecords.map((record: any) => (
                      <tr key={record.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {record.employee?.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatDate(record.payrollDate)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.baseSalary)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.overtimeAmount)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.bonuses)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.deductions)}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {formatCurrency(record.netPay)}
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={getStatusColor(record.status)}>
                            {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center space-y-3">
                          <FileText className="h-12 w-12 text-gray-400" />
                          <div>
                            <p className="text-gray-500 font-medium">No payroll records found</p>
                            <p className="text-gray-400 text-sm">Process payroll for your employees</p>
                          </div>
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Process Payroll
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
