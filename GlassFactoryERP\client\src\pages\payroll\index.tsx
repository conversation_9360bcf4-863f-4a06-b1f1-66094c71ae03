import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { formatCurrency, formatDate, getStatusColor } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { 
  Users, 
  DollarSign, 
  Calculator, 
  FileText,
  Plus,
  Search,
  Edit,
  Trash2,
  Settings,
  CalendarIcon,
  Loader2,
  Download,
  Eye
} from "lucide-react";
import { format } from "date-fns";

export default function PayrollIndex() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<"employees" | "payroll">("employees");
  const [employeeModal, setEmployeeModal] = useState({ open: false, employee: null, mode: "create" as "create" | "edit" });
  const [payrollProcessModal, setPayrollProcessModal] = useState({ open: false });
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [payPeriod, setPayPeriod] = useState({ start: new Date(), end: new Date() });

  const { data: employees, isLoading: employeesLoading } = useQuery({
    queryKey: ["/api/employees"],
  });

  const { data: payrollRecords, isLoading: payrollLoading } = useQuery({
    queryKey: ["/api/payroll"],
  });

  const { data: payrollSummary } = useQuery({
    queryKey: ["/api/payroll/reports/summary"],
  });

  const filteredEmployees = (employees as any[])?.filter((employee: any) =>
    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.position?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const filteredPayrollRecords = (payrollRecords as any[])?.filter((record: any) =>
    record.employee?.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const processPayrollMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch("/api/payroll/process", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error("Failed to process payroll");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payroll"] });
      queryClient.invalidateQueries({ queryKey: ["/api/payroll/reports/summary"] });
      toast({ title: "Success", description: "Payroll processed successfully" });
      setPayrollProcessModal({ open: false });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to process payroll", variant: "destructive" });
    }
  });

  const deleteEmployeeMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/employees/${id}`, { method: "DELETE" });
      if (!response.ok) throw new Error("Failed to delete employee");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/employees"] });
      toast({ title: "Success", description: "Employee deleted successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to delete employee", variant: "destructive" });
    }
  });

  const deletePayrollMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/payroll/${id}`, { method: "DELETE" });
      if (!response.ok) throw new Error("Failed to delete payroll record");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/payroll"] });
      toast({ title: "Success", description: "Payroll record deleted successfully" });
    },
    onError: () => {
      toast({ title: "Error", description: "Failed to delete payroll record", variant: "destructive" });
    }
  });

  const handleProcessPayroll = () => {
    if (selectedEmployees.length === 0) {
      toast({ title: "Error", description: "Please select employees", variant: "destructive" });
      return;
    }

    processPayrollMutation.mutate({
      employeeIds: selectedEmployees,
      payPeriodStart: payPeriod.start,
      payPeriodEnd: payPeriod.end
    });
  };

  if (employeesLoading || payrollLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Payroll Management</h2>
        <p className="text-slate-600">Manage employees, salaries, and payroll operations</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Employees</p>
                <p className="text-2xl font-bold text-slate-800">{(employees as any[])?.length || 0}</p>
                <p className="text-emerald-600 text-sm font-medium">Active workforce</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Payroll</p>
                <p className="text-2xl font-bold text-slate-800">
                  {formatCurrency((payrollSummary as any)?.totalNetPay || 0)}
                </p>
                <p className="text-slate-600 text-sm font-medium">This period</p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Avg. Salary</p>
                <p className="text-2xl font-bold text-slate-800">
                  {formatCurrency((payrollSummary as any)?.averageSalary || 0)}
                </p>
                <p className="text-slate-600 text-sm font-medium">Per employee</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calculator className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Pending</p>
                <p className="text-2xl font-bold text-slate-800">
                  {(payrollRecords as any[])?.filter((r: any) => r.status === 'pending').length || 0}
                </p>
                <p className="text-amber-600 text-sm font-medium">Awaiting processing</p>
              </div>
              <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6">
        <Button
          variant={activeTab === "employees" ? "default" : "outline"}
          onClick={() => setActiveTab("employees")}
        >
          Employees
        </Button>
        <Button
          variant={activeTab === "payroll" ? "default" : "outline"}
          onClick={() => setActiveTab("payroll")}
        >
          Payroll Records
        </Button>
      </div>

      {/* Search and Actions */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={activeTab === "employees" ? "Search employees..." : "Search payroll records..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              {activeTab === "employees" && (
                <Button onClick={() => setEmployeeModal({ open: true, employee: null, mode: "create" })}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Employee
                </Button>
              )}
              {activeTab === "payroll" && (
                <Button onClick={() => setPayrollProcessModal({ open: true })}>
                  <Plus className="h-4 w-4 mr-2" />
                  Process Payroll
                </Button>
              )}
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content based on active tab */}
      {activeTab === "employees" ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Employees</span>
              <Badge variant="secondary">{filteredEmployees.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Code</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Name</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Position</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Branch</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Basic Salary</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Working Hours</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {filteredEmployees.length ? (
                    filteredEmployees.map((employee: any) => (
                      <tr key={employee.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {employee.code}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.position || "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.branch || "-"}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {employee.basicSalary ? formatCurrency(employee.basicSalary) : "-"}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {employee.workingHours || 8} hours
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={getStatusColor(employee.active ? "active" : "inactive")}>
                            {employee.active ? "Active" : "Inactive"}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => setEmployeeModal({ open: true, employee, mode: "edit" })}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => deleteEmployeeMutation.mutate(employee.id)}
                              disabled={deleteEmployeeMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={8} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center space-y-3">
                          <Users className="h-12 w-12 text-gray-400" />
                          <div>
                            <p className="text-gray-500 font-medium">No employees found</p>
                            <p className="text-gray-400 text-sm">Add employees to get started with payroll</p>
                          </div>
                          <Button onClick={() => setEmployeeModal({ open: true, employee: null, mode: "create" })}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Employee
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Payroll Records</span>
              <Badge variant="secondary">{filteredPayrollRecords.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Employee</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Pay Period</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Base Salary</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Overtime</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Bonuses</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Deductions</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Net Pay</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Status</th>
                    <th className="text-left px-6 py-3 text-sm font-medium text-slate-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {filteredPayrollRecords.length ? (
                    filteredPayrollRecords.map((record: any) => (
                      <tr key={record.id} className="hover:bg-slate-50">
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {record.employee?.name}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatDate(record.payPeriodStart)} - {formatDate(record.payPeriodEnd)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.basicPay)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.overtime)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.bonuses)}
                        </td>
                        <td className="px-6 py-4 text-sm text-slate-600">
                          {formatCurrency(record.deductions)}
                        </td>
                        <td className="px-6 py-4 text-sm font-medium text-slate-800">
                          {formatCurrency(record.netPay)}
                        </td>
                        <td className="px-6 py-4">
                          <Badge className={getStatusColor(record.status)}>
                            {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => deletePayrollMutation.mutate(record.id)}
                              disabled={deletePayrollMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center space-y-3">
                          <FileText className="h-12 w-12 text-gray-400" />
                          <div>
                            <p className="text-gray-500 font-medium">No payroll records found</p>
                            <p className="text-gray-400 text-sm">Process payroll for your employees</p>
                          </div>
                          <Button onClick={() => setPayrollProcessModal({ open: true })}>
                            <Plus className="h-4 w-4 mr-2" />
                            Process Payroll
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employee Modal */}
      <Dialog open={employeeModal.open} onOpenChange={(open) => setEmployeeModal({ ...employeeModal, open })}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {employeeModal.mode === "create" ? "Add New Employee" : "Edit Employee"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Name</Label>
                <Input placeholder="Employee name" />
              </div>
              <div>
                <Label>Position</Label>
                <Input placeholder="Position" />
              </div>
              <div>
                <Label>Basic Salary</Label>
                <Input type="number" placeholder="0.00" />
              </div>
              <div>
                <Label>Working Hours</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select hours" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="8">8 hours</SelectItem>
                    <SelectItem value="9">9 hours</SelectItem>
                    <SelectItem value="10">10 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setEmployeeModal({ ...employeeModal, open: false })}>
                Cancel
              </Button>
              <Button>Save Employee</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Payroll Process Modal */}
      <Dialog open={payrollProcessModal.open} onOpenChange={(open) => setPayrollProcessModal({ open })}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Process Payroll</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Pay Period Start</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {payPeriod.start ? format(payPeriod.start, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={payPeriod.start}
                      onSelect={(date) => setPayPeriod({ ...payPeriod, start: date || new Date() })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <Label>Pay Period End</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {payPeriod.end ? format(payPeriod.end, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={payPeriod.end}
                      onSelect={(date) => setPayPeriod({ ...payPeriod, end: date || new Date() })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div>
              <Label>Select Employees</Label>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2 space-y-2">
                {(employees as any[])?.map((employee: any) => (
                  <div key={employee.id} className="flex items-center space-x-2">
                    <Switch
                      checked={selectedEmployees.includes(employee.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedEmployees([...selectedEmployees, employee.id]);
                        } else {
                          setSelectedEmployees(selectedEmployees.filter(id => id !== employee.id));
                        }
                      }}
                    />
                    <span className="text-sm">{employee.name} - {employee.position}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setPayrollProcessModal({ open: false })}>
                Cancel
              </Button>
              <Button 
                onClick={handleProcessPayroll}
                disabled={processPayrollMutation.isPending || selectedEmployees.length === 0}
              >
                {processPayrollMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Process Payroll
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
