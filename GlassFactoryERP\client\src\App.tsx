import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { LanguageProvider } from "@/contexts/LanguageContext";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/dashboard";
import CompanySettings from "@/pages/company-settings";
import SalesIndex from "@/pages/sales/index";
import SalesInvoices from "@/pages/sales/invoices";
import SalesCustomers from "@/pages/sales/customers";
import PurchasesIndex from "@/pages/purchases/index";
import InventoryIndex from "@/pages/inventory/index";
import PayrollIndex from "@/pages/payroll/index";
import AccountingIndex from "@/pages/accounting/index";
import Sidebar from "@/components/layout/sidebar";

function Router() {
  return (
    <div className="flex min-h-screen">
      <Sidebar />
      <div className="flex-1 ml-64">
        <Switch>
          <Route path="/" component={Dashboard} />
          <Route path="/dashboard" component={Dashboard} />
          <Route path="/company-settings" component={CompanySettings} />
          <Route path="/sales" component={SalesIndex} />
          <Route path="/sales/invoices" component={SalesInvoices} />
          <Route path="/sales/customers" component={SalesCustomers} />
          <Route path="/purchases" component={PurchasesIndex} />
          <Route path="/inventory" component={InventoryIndex} />
          <Route path="/payroll" component={PayrollIndex} />
          <Route path="/accounting" component={AccountingIndex} />
          <Route component={NotFound} />
        </Switch>
      </div>
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <LanguageProvider>
          <Toaster />
          <Router />
        </LanguageProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
