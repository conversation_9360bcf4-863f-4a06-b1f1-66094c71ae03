# Assets Directory

This directory contains application assets including icons for different platforms.

## Required Icons

For a complete Electron application, you should add the following icon files:

- `icon.png` - Main application icon (512x512 or larger)
- `icon.ico` - Windows icon file
- `icon.icns` - macOS icon file

## Icon Requirements

### Windows (.ico)
- Multiple sizes: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256
- Format: ICO

### macOS (.icns)
- Multiple sizes: 16x16, 32x32, 128x128, 256x256, 512x512, 1024x1024
- Format: ICNS

### Linux (.png)
- Size: 512x512 or larger
- Format: PNG

## Creating Icons

You can use online tools or software like:
- [Electron Icon Maker](https://www.electronjs.org/docs/latest/tutorial/icon-creation)
- [Icon Generator](https://icon.kitchen/)
- Adobe Photoshop/GIMP for manual creation

## Current Status

Currently using placeholder icons. Replace with actual Glass Factory ERP branding icons.
