import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Truck, 
  Users, 
  FileText, 
  Package,
  DollarSign,
  TrendingDown,
  Plus
} from "lucide-react";

export default function PurchasesIndex() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Purchases Management</h2>
        <p className="text-slate-600">Manage suppliers, purchase orders, and inventory receipts</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">This Month Purchases</p>
                <p className="text-2xl font-bold text-slate-800">$45,280</p>
                <p className="text-red-600 text-sm font-medium">+8.2% from last month</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Purchase Orders</p>
                <p className="text-2xl font-bold text-slate-800">23</p>
                <p className="text-blue-600 text-sm font-medium">5 pending approval</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Active Suppliers</p>
                <p className="text-2xl font-bold text-slate-800">12</p>
                <p className="text-emerald-600 text-sm font-medium">2 new this month</p>
              </div>
              <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Suppliers */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <span>Suppliers</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              Manage supplier database with contact details, payment terms, and transaction history.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Supplier
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Purchase Orders */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-emerald-600" />
              </div>
              <span>Purchase Orders</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              Create and manage purchase orders with automatic invoice generation and accounting entries.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Create PO
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Stock Receipts */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Package className="h-5 w-5 text-purple-600" />
              </div>
              <span>Stock Receipts</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              Process incoming inventory with automatic purchase invoice creation and stock updates.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Receive Stock
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Purchase Invoices */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center">
                <Truck className="h-5 w-5 text-amber-600" />
              </div>
              <span>Purchase Invoices</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              View and manage automatically generated purchase invoices with payment tracking.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                View Invoices
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Payments */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-red-600" />
              </div>
              <span>Supplier Payments</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              Process payments to suppliers and maintain account statements with automatic accounting.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                Make Payment
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Reports */}
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <TrendingDown className="h-5 w-5 text-gray-600" />
              </div>
              <span>Purchase Reports</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-slate-600 mb-4">
              Generate purchase reports, supplier statements, and cost analysis reports.
            </p>
            <div className="space-y-2">
              <Button className="w-full" variant="outline">
                Generate Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
