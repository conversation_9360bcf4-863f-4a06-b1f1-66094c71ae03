import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Settings, 
  Save, 
  FileText,
  Calculator,
  Percent,
  Building
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface SalesSettings {
  id?: number;
  taxRate: number;
  defaultPaymentTerms: number;
  invoicePrefix: string;
  invoiceFooter: string;
  companyInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
    taxId: string;
  };
  accountingSettings: {
    salesAccountId: number;
    taxAccountId: number;
    receivablesAccountId: number;
  };
  invoiceTemplate: string;
  autoGenerateInvoiceNumbers: boolean;
  requireCustomerApproval: boolean;
  sendEmailNotifications: boolean;
}

export default function SalesSettingsPage() {
  const [activeTab, setActiveTab] = useState("general");
  
  const queryClient = useQueryClient();

  // Fetch sales settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ["sales-settings"],
    queryFn: async () => {
      const response = await fetch("/api/sales/settings");
      if (!response.ok) throw new Error("Failed to fetch sales settings");
      return response.json();
    }
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (settingsData: Partial<SalesSettings>) => {
      const response = await fetch("/api/sales/settings", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(settingsData)
      });
      if (!response.ok) throw new Error("Failed to update sales settings");
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["sales-settings"] });
    }
  });

  const [formData, setFormData] = useState<SalesSettings>({
    taxRate: 0,
    defaultPaymentTerms: 30,
    invoicePrefix: "INV",
    invoiceFooter: "",
    companyInfo: {
      name: "",
      address: "",
      phone: "",
      email: "",
      taxId: ""
    },
    accountingSettings: {
      salesAccountId: 0,
      taxAccountId: 0,
      receivablesAccountId: 0
    },
    invoiceTemplate: "standard",
    autoGenerateInvoiceNumbers: true,
    requireCustomerApproval: false,
    sendEmailNotifications: true
  });

  // Update form data when settings are loaded
  useState(() => {
    if (settings) {
      setFormData(settings);
    }
  }, [settings]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateSettingsMutation.mutate(formData);
  };

  const updateFormData = (path: string, value: any) => {
    setFormData(prev => {
      const keys = path.split('.');
      const newData = { ...prev };
      let current: any = newData;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  if (isLoading) {
    return <div className="p-6">Loading sales settings...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-semibold text-slate-800 mb-2">Sales Settings</h2>
        <p className="text-slate-600">Configure sales processes and invoice templates</p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: "general", label: "General", icon: Settings },
              { id: "company", label: "Company Info", icon: Building },
              { id: "accounting", label: "Accounting", icon: Calculator },
              { id: "templates", label: "Templates", icon: FileText }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* General Settings */}
        {activeTab === "general" && (
          <Card>
            <CardHeader>
              <CardTitle>General Sales Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="taxRate">Default Tax Rate (%)</Label>
                  <div className="relative">
                    <Input
                      id="taxRate"
                      type="number"
                      step="0.01"
                      value={formData.taxRate}
                      onChange={(e) => updateFormData("taxRate", parseFloat(e.target.value) || 0)}
                    />
                    <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="paymentTerms">Default Payment Terms (days)</Label>
                  <Input
                    id="paymentTerms"
                    type="number"
                    value={formData.defaultPaymentTerms}
                    onChange={(e) => updateFormData("defaultPaymentTerms", parseInt(e.target.value) || 30)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="invoicePrefix">Invoice Number Prefix</Label>
                  <Input
                    id="invoicePrefix"
                    value={formData.invoicePrefix}
                    onChange={(e) => updateFormData("invoicePrefix", e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="invoiceTemplate">Invoice Template</Label>
                  <select
                    id="invoiceTemplate"
                    value={formData.invoiceTemplate}
                    onChange={(e) => updateFormData("invoiceTemplate", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="standard">Standard</option>
                    <option value="detailed">Detailed</option>
                    <option value="minimal">Minimal</option>
                  </select>
                </div>
              </div>

              <div>
                <Label htmlFor="invoiceFooter">Invoice Footer Text</Label>
                <Textarea
                  id="invoiceFooter"
                  value={formData.invoiceFooter}
                  onChange={(e) => updateFormData("invoiceFooter", e.target.value)}
                  rows={3}
                  placeholder="Thank you for your business..."
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="autoGenerate"
                    checked={formData.autoGenerateInvoiceNumbers}
                    onChange={(e) => updateFormData("autoGenerateInvoiceNumbers", e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="autoGenerate">Auto-generate invoice numbers</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="requireApproval"
                    checked={formData.requireCustomerApproval}
                    onChange={(e) => updateFormData("requireCustomerApproval", e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="requireApproval">Require customer approval before finalizing</Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="emailNotifications"
                    checked={formData.sendEmailNotifications}
                    onChange={(e) => updateFormData("sendEmailNotifications", e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="emailNotifications">Send email notifications</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Company Information */}
        {activeTab === "company" && (
          <Card>
            <CardHeader>
              <CardTitle>Company Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="companyName">Company Name</Label>
                <Input
                  id="companyName"
                  value={formData.companyInfo.name}
                  onChange={(e) => updateFormData("companyInfo.name", e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="companyAddress">Address</Label>
                <Textarea
                  id="companyAddress"
                  value={formData.companyInfo.address}
                  onChange={(e) => updateFormData("companyInfo.address", e.target.value)}
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="companyPhone">Phone</Label>
                  <Input
                    id="companyPhone"
                    value={formData.companyInfo.phone}
                    onChange={(e) => updateFormData("companyInfo.phone", e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="companyEmail">Email</Label>
                  <Input
                    id="companyEmail"
                    type="email"
                    value={formData.companyInfo.email}
                    onChange={(e) => updateFormData("companyInfo.email", e.target.value)}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="taxId">Tax ID / Registration Number</Label>
                <Input
                  id="taxId"
                  value={formData.companyInfo.taxId}
                  onChange={(e) => updateFormData("companyInfo.taxId", e.target.value)}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Accounting Settings */}
        {activeTab === "accounting" && (
          <Card>
            <CardHeader>
              <CardTitle>Accounting Integration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <Label htmlFor="salesAccount">Sales Account</Label>
                  <select
                    id="salesAccount"
                    value={formData.accountingSettings.salesAccountId}
                    onChange={(e) => updateFormData("accountingSettings.salesAccountId", parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={0}>Select Account</option>
                    <option value={4001}>4001 - Sales Revenue</option>
                    <option value={4002}>4002 - Service Revenue</option>
                  </select>
                </div>
                
                <div>
                  <Label htmlFor="taxAccount">Tax Account</Label>
                  <select
                    id="taxAccount"
                    value={formData.accountingSettings.taxAccountId}
                    onChange={(e) => updateFormData("accountingSettings.taxAccountId", parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={0}>Select Account</option>
                    <option value={2301}>2301 - Sales Tax Payable</option>
                    <option value={2302}>2302 - VAT Payable</option>
                  </select>
                </div>
                
                <div>
                  <Label htmlFor="receivablesAccount">Accounts Receivable</Label>
                  <select
                    id="receivablesAccount"
                    value={formData.accountingSettings.receivablesAccountId}
                    onChange={(e) => updateFormData("accountingSettings.receivablesAccountId", parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={0}>Select Account</option>
                    <option value={1201}>1201 - Accounts Receivable</option>
                    <option value={1202}>1202 - Trade Receivables</option>
                  </select>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Automatic Journal Entries</h4>
                <p className="text-blue-700 text-sm">
                  When invoices are created, the system will automatically generate journal entries using the accounts configured above.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Templates */}
        {activeTab === "templates" && (
          <Card>
            <CardHeader>
              <CardTitle>Invoice Templates</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="border rounded-lg p-4 cursor-pointer hover:border-blue-500">
                  <div className="aspect-[3/4] bg-gray-100 rounded mb-3 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <h4 className="font-medium">Standard Template</h4>
                  <p className="text-sm text-gray-600">Clean and professional layout</p>
                </div>
                
                <div className="border rounded-lg p-4 cursor-pointer hover:border-blue-500">
                  <div className="aspect-[3/4] bg-gray-100 rounded mb-3 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <h4 className="font-medium">Detailed Template</h4>
                  <p className="text-sm text-gray-600">Includes detailed line items</p>
                </div>
                
                <div className="border rounded-lg p-4 cursor-pointer hover:border-blue-500">
                  <div className="aspect-[3/4] bg-gray-100 rounded mb-3 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <h4 className="font-medium">Minimal Template</h4>
                  <p className="text-sm text-gray-600">Simple and compact design</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Save Button */}
        <div className="mt-6">
          <Button 
            type="submit" 
            disabled={updateSettingsMutation.isPending}
            className="w-full sm:w-auto"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </div>
      </form>
    </div>
  );
}
