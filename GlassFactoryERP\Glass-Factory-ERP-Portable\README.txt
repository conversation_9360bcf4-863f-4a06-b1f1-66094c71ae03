========================================
نظام إدارة مصنع الزجاج - Glass Factory ERP
========================================

هذا هو الملف المحمول لنظام إدارة مصنع الزجاج الذي يعمل بدون تثبيت.

طرق التشغيل:
============

الطريقة الأولى (الأسهل):
- انقر نقراً مزدوجاً على ملف "تشغيل نظام إدارة مصنع الزجاج.bat"

الطريقة الثانية:
- انقر نقراً مزدوجاً على ملف "Glass Factory ERP.exe"

مسار التشغيل:
=============
Glass Factory ERP.exe

ملاحظة مهمة:
============
- سيتم فتح أدوات المطور تلقائياً لاستكشاف الأخطاء
- إذا ظهرت شاشة بيضاء، تحقق من وحدة التحكم (Console) في أدوات المطور
- ابحث عن أي رسائل خطأ باللون الأحمر
- يجب أن ترى رسالة "Test page loaded successfully" في وحدة التحكم

استكشاف الأخطاء:
================
إذا ظهرت شاشة بيضاء:
1. افتح أدوات المطور (F12)
2. انتقل إلى تبويب Console
3. ابحث عن رسائل الخطأ
4. تأكد من وجود الملفات في مجلد resources
5. جرب إعادة تشغيل التطبيق

الملفات المطلوبة:
================
- Glass Factory ERP.exe (الملف الرئيسي)
- resources/ (مجلد الموارد)
- locales/ (مجلد اللغات)
- جميع ملفات .dll و .pak

========================================
Glass Factory ERP - Portable Version
========================================

This is the portable version of Glass Factory ERP that runs without installation.

How to Run:
===========

Method 1 (Easiest):
- Double-click on "تشغيل نظام إدارة مصنع الزجاج.bat"

Method 2:
- Double-click on "Glass Factory ERP.exe"

Executable Path:
===============
Glass Factory ERP.exe

Important Note:
==============
- Developer tools will open automatically for debugging
- If you see a white screen, check the Console tab in developer tools
- Look for any error messages in red
- You should see "Test page loaded successfully" message in console

Troubleshooting:
===============
If you see a white screen:
1. Open developer tools (F12)
2. Go to Console tab
3. Look for error messages
4. Make sure files exist in resources folder
5. Try restarting the application

Required Files:
==============
- Glass Factory ERP.exe (main executable)
- resources/ (resources folder)
- locales/ (languages folder)
- All .dll and .pak files
