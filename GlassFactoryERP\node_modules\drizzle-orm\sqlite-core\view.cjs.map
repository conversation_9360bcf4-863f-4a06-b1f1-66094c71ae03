{"version": 3, "sources": ["../../src/sqlite-core/view.ts"], "sourcesContent": ["import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { SQLiteColumn, SQLiteColumnBuilderBase } from './columns/common.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport { sqliteTable } from './table.ts';\nimport { SQLiteViewBase } from './view-base.ts';\n\nexport interface ViewBuilderConfig {\n\talgorithm?: 'undefined' | 'merge' | 'temptable';\n\tdefiner?: string;\n\tsqlSecurity?: 'definer' | 'invoker';\n\twithCheckOption?: 'cascaded' | 'local';\n}\n\nexport class ViewBuilderCore<\n\tTConfig extends { name: string; columns?: unknown },\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteViewBuilderCore';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t) {}\n\n\tprotected config: ViewBuilderConfig = {};\n}\n\nexport class ViewBuilder<TName extends string = string> extends ViewBuilderCore<{ name: TName }> {\n\tstatic override readonly [entityKind]: string = 'SQLiteViewBuilder';\n\n\tas<TSelection extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t): SQLiteViewWithSelection<TName, false, AddAliasToSelection<TSelection, TName, 'sqlite'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelection>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\t// const aliasedSelectedFields = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\tconst aliasedSelectedFields = qb.getSelectedFields();\n\t\treturn new Proxy(\n\t\t\tnew SQLiteView({\n\t\t\t\t// sqliteConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: undefined,\n\t\t\t\t\tselectedFields: aliasedSelectedFields,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as SQLiteViewWithSelection<TName, false, AddAliasToSelection<TSelection, TName, 'sqlite'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, SQLiteColumnBuilderBase> = Record<string, SQLiteColumnBuilderBase>,\n> extends ViewBuilderCore<\n\t{ name: TName; columns: TColumns }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteManualViewBuilder';\n\n\tprivate columns: Record<string, SQLiteColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t) {\n\t\tsuper(name);\n\t\tthis.columns = getTableColumns(sqliteTable(name, columns)) as BuildColumns<TName, TColumns, 'sqlite'>;\n\t}\n\n\texisting(): SQLiteViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'sqlite'>> {\n\t\treturn new Proxy(\n\t\t\tnew SQLiteView({\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: undefined,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as SQLiteViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'sqlite'>>;\n\t}\n\n\tas(query: SQL): SQLiteViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'sqlite'>> {\n\t\treturn new Proxy(\n\t\t\tnew SQLiteView({\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: undefined,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as SQLiteViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'sqlite'>>;\n\t}\n}\n\nexport class SQLiteView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n> extends SQLiteViewBase<TName, TExisting, TSelection> {\n\tstatic override readonly [entityKind]: string = 'SQLiteView';\n\n\tconstructor({ config }: {\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t}\n}\n\nexport type SQLiteViewWithSelection<\n\tTName extends string,\n\tTExisting extends boolean,\n\tTSelection extends ColumnsSelection,\n> = SQLiteView<TName, TExisting, TSelection> & TSelection;\n\nexport function sqliteView<TName extends string>(name: TName): ViewBuilder<TName>;\nexport function sqliteView<TName extends string, TColumns extends Record<string, SQLiteColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualViewBuilder<TName, TColumns>;\nexport function sqliteView(\n\tname: string,\n\tselection?: Record<string, SQLiteColumnBuilderBase>,\n): ViewBuilder | ManualViewBuilder {\n\tif (selection) {\n\t\treturn new ManualViewBuilder(name, selection);\n\t}\n\treturn new ViewBuilder(name);\n}\n\nexport const view = sqliteView;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAG3B,6BAAsC;AAEtC,mBAAgC;AAEhC,2BAA6B;AAC7B,mBAA4B;AAC5B,uBAA+B;AASxB,MAAM,gBAEX;AAAA,EAQD,YACW,MACT;AADS;AAAA,EACR;AAAA,EATH,QAAiB,wBAAU,IAAY;AAAA,EAW7B,SAA4B,CAAC;AACxC;AAEO,MAAM,oBAAmD,gBAAiC;AAAA,EAChG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,GACC,IAC0F;AAC1F,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,kCAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,6CAAkC;AAAA,MAC5D,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AAED,UAAM,wBAAwB,GAAG,kBAAkB;AACnD,WAAO,IAAI;AAAA,MACV,IAAI,WAAW;AAAA;AAAA,QAEd,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,gBAER;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAER,YACC,MACA,SACC;AACD,UAAM,IAAI;AACV,SAAK,cAAU,kCAAgB,0BAAY,MAAM,OAAO,CAAC;AAAA,EAC1D;AAAA,EAEA,WAA0F;AACzF,WAAO,IAAI;AAAA,MACV,IAAI,WAAW;AAAA,QACd,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAA4F;AAC9F,WAAO,IAAI;AAAA,MACV,IAAI,WAAW;AAAA,QACd,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ;AAAA,UACR,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,6CAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,MAAM,mBAIH,gCAA6C;AAAA,EACtD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,EAAE,OAAO,GAOlB;AACF,UAAM,MAAM;AAAA,EACb;AACD;AAaO,SAAS,WACf,MACA,WACkC;AAClC,MAAI,WAAW;AACd,WAAO,IAAI,kBAAkB,MAAM,SAAS;AAAA,EAC7C;AACA,SAAO,IAAI,YAAY,IAAI;AAC5B;AAEO,MAAM,OAAO;", "names": []}