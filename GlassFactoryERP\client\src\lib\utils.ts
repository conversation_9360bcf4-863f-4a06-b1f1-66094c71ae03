import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: string | number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Number(amount));
}

export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function calculateSquareMeters(length: number, width: number, quantity: number): number {
  return (length * width * quantity) / 1000000;
}

export function generateCode(prefix: string, count: number): string {
  return `${prefix}-${count.toString().padStart(4, '0')}`;
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'final':
    case 'paid':
    case 'active':
    case 'completed':
      return 'bg-emerald-100 text-emerald-800';
    case 'draft':
    case 'pending':
      return 'bg-amber-100 text-amber-800';
    case 'cancelled':
    case 'inactive':
      return 'bg-red-100 text-red-800';
    case 'low stock':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
